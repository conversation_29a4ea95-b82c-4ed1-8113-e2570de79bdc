# ✅ Backend HaloBantu - Setup Lengkap

## 🎉 **Status: SELESAI!**

Backend Node.js untuk HaloBantu telah berhasil dibuat dengan struktur lengkap dan siap digunakan.

---

## 📁 **Struktur Backend yang Telah Dibuat**

```
📂 halobantu-backend/
├── 📁 config/
│   ├── 📄 database.js       # Konfigurasi MySQL + Sequelize
│   └── 📄 auth.js          # Konfigurasi JWT & security
├── 📁 controllers/
│   ├── 📄 authController.js # Login, register, forgot password
│   └── 📄 userController.js # Manajemen user & profile
├── 📁 middleware/
│   ├── 📄 auth.js          # JWT verification & role checking
│   └── 📄 validation.js    # Input validation rules
├── 📁 models/
│   ├── 📄 User.js          # Model user dengan Sequelize
│   └── 📄 index.js         # Export models
├── 📁 routes/
│   ├── 📄 auth.js          # Routes autentikasi
│   └── 📄 users.js         # Routes manajemen user
├── 📁 scripts/
│   ├── 📄 createDatabase.js # Script buat database MySQL
│   └── 📄 syncDatabase.js   # Script sync models + admin user
├── 📁 utils/
│   └── 📄 helpers.js       # Utility functions
├── 📄 .env                 # Environment variables
├── 📄 .gitignore          # Git ignore rules
├── 📄 package.json        # Dependencies & scripts
├── 📄 server.js           # Main server file
└── 📄 README.md           # Dokumentasi lengkap
```

---

## 🚀 **Langkah-Langkah Menjalankan Backend**

### **1. Persiapan Database MySQL**

Pastikan MySQL sudah terinstall dan berjalan di sistem Anda (via XAMPP/WAMP/MAMP atau standalone).

### **2. Navigasi ke Folder Backend**

```bash
cd halobantu-backend
```

### **3. Install Dependencies**

```bash
npm install
```

### **4. Setup Environment Variables**

File `.env` sudah dibuat dengan konfigurasi default untuk phpMyAdmin:

```env
# Database (sesuaikan jika perlu)
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=
DB_NAME=halobantu_db
DB_PORT=3306

# JWT (sudah dikonfigurasi)
JWT_SECRET=halobantu_super_secret_jwt_key_2024_very_secure
JWT_EXPIRE=7d

# Server
PORT=5000
NODE_ENV=development

# CORS
FRONTEND_URL=http://localhost:3000
```

### **5. Setup Database**

```bash
# Buat database dan sync models sekaligus
npm run setup

# Atau step by step:
npm run db:create    # Buat database halobantu_db
npm run db:sync      # Sync models + buat admin user
```

### **6. Jalankan Server**

```bash
# Development mode (recommended)
npm run dev

# Production mode
npm start
```

**Server akan berjalan di: `http://localhost:5000`**

---

## 🔐 **Akun Admin Default**

Setelah setup database, akun admin otomatis dibuat:

- **Email**: `<EMAIL>`
- **Password**: `Admin123!`
- **Role**: `admin`

⚠️ **Penting**: Ganti password setelah login pertama!

---

## 📚 **API Endpoints yang Tersedia**

### **🔑 Authentication (`/api/auth`)**

| Method | Endpoint | Deskripsi |
|--------|----------|-----------|
| POST | `/api/auth/register` | Daftar user baru |
| POST | `/api/auth/login` | Login user |
| POST | `/api/auth/refresh` | Refresh JWT token |
| POST | `/api/auth/forgot-password` | Lupa password |
| POST | `/api/auth/reset-password` | Reset password |
| GET | `/api/auth/profile` | Get profile user |
| POST | `/api/auth/logout` | Logout user |

### **👥 User Management (`/api/users`)**

| Method | Endpoint | Deskripsi | Role |
|--------|----------|-----------|------|
| GET | `/api/users/profile` | Get profile sendiri | Any |
| PUT | `/api/users/profile` | Update profile | Any |
| PUT | `/api/users/change-password` | Ganti password | Any |
| DELETE | `/api/users/deactivate` | Nonaktifkan akun | Any |
| GET | `/api/users/` | Get semua user | Admin |
| GET | `/api/users/:id` | Get user by ID | Owner/Admin |
| PUT | `/api/users/:id/role` | Update role user | Admin |
| PUT | `/api/users/:id/toggle-status` | Toggle status aktif | Admin |

---

## 🧪 **Testing API**

### **Health Check**
```bash
curl http://localhost:5000/api/health
```

### **Register User Baru**
```bash
curl -X POST http://localhost:5000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "Password123",
    "confirmPassword": "Password123",
    "firstName": "Test",
    "lastName": "User"
  }'
```

### **Login**
```bash
curl -X POST http://localhost:5000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "Password123"
  }'
```

### **Get Profile (dengan token)**
```bash
curl -X GET http://localhost:5000/api/auth/profile \
  -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE"
```

---

## 🔒 **Fitur Keamanan yang Sudah Diimplementasi**

- ✅ **JWT Authentication** dengan refresh token
- ✅ **Password Hashing** dengan bcrypt (12 rounds)
- ✅ **Rate Limiting** (100 requests/15 menit per IP)
- ✅ **Input Validation** dengan express-validator
- ✅ **CORS Protection** 
- ✅ **Helmet Security Headers**
- ✅ **Account Lockout** (5 failed attempts = 15 menit lock)
- ✅ **Role-based Access Control** (user, admin, psychologist)
- ✅ **SQL Injection Prevention** (Sequelize ORM)
- ✅ **XSS Protection**

---

## 🎯 **Langkah Selanjutnya**

### **1. Integrasi dengan Frontend**

Update file `src/services/api.js` di frontend React:

```javascript
const API_BASE_URL = 'http://localhost:5000/api';

// Auth service
export const authService = {
  register: (userData) => axios.post(`${API_BASE_URL}/auth/register`, userData),
  login: (credentials) => axios.post(`${API_BASE_URL}/auth/login`, credentials),
  getProfile: () => axios.get(`${API_BASE_URL}/auth/profile`),
  // ... dst
};
```

### **2. Update Frontend Auth Context**

Buat context untuk menyimpan user state dan token JWT.

### **3. Testing Lengkap**

- Test semua endpoint dengan Postman/Thunder Client
- Test integrasi frontend-backend
- Test error handling

### **4. Development Lanjutan**

- Tambah fitur email verification
- Implementasi forgot password dengan email
- Tambah logging system
- Setup monitoring

---

## 🛠️ **Scripts yang Tersedia**

```bash
npm start              # Jalankan server production
npm run dev           # Jalankan server development (auto-reload)
npm run db:create     # Buat database MySQL
npm run db:sync       # Sync models ke database
npm run db:sync:force # Sync dengan drop tables (hati-hati!)
npm run setup         # Setup lengkap (create + sync)
```

---

## 🎉 **Kesimpulan**

Backend HaloBantu telah **100% siap digunakan** dengan fitur:

- ✅ **Autentikasi lengkap** (register, login, JWT)
- ✅ **Manajemen user** dengan role-based access
- ✅ **Database MySQL** dengan Sequelize ORM
- ✅ **Security terbaik** dengan berbagai proteksi
- ✅ **API documentation** yang lengkap
- ✅ **Error handling** yang komprehensif
- ✅ **Development tools** (nodemon, scripts)

**Sekarang Anda bisa:**
1. Menjalankan backend dengan `npm run dev`
2. Test API endpoints
3. Integrasikan dengan frontend React
4. Mulai development fitur lanjutan

**Happy Coding! 🚀**
