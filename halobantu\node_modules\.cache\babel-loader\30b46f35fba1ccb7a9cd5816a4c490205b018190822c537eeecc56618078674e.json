{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\JOKI MAKALAH\\\\halobantu1\\\\halobantu\\\\src\\\\features\\\\auth\\\\Login.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { FaUser, FaLock, FaEye, FaEyeSlash, FaGoogle, FaFacebook } from 'react-icons/fa';\nimport authService from '../../services/authService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Login = () => {\n  _s();\n  const [showPassword, setShowPassword] = useState(false);\n  const [formData, setFormData] = useState({\n    email: '',\n    password: ''\n  });\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const navigate = useNavigate();\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    // Clear error when user starts typing\n    if (error) setError('');\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n    try {\n      const response = await authService.login(formData);\n      if (response.success) {\n        const user = response.data.user;\n\n        // Redirect berdasarkan role user\n        if (user.role === 'admin') {\n          navigate('/admin');\n        } else {\n          navigate('/dashboard');\n        }\n      }\n    } catch (error) {\n      console.error('Login error:', error);\n      setError(error.message || 'Login gagal. Silakan coba lagi.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"h-screen flex overflow-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"hidden lg:flex lg:w-1/2 bg-gradient-to-br from-blue-600 to-purple-700 relative overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 bg-black bg-opacity-20\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n        src: \"https://images.pexels.com/photos/3184465/pexels-photo-3184465.jpeg?auto=compress&cs=tinysrgb&w=800&h=1200&fit=crop\",\n        alt: \"Login Background\",\n        className: \"w-full h-full object-cover\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 flex items-center justify-center\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center text-white p-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-4xl font-bold mb-4\",\n            children: \"Selamat Datang di Future X\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl opacity-90\",\n            children: \"Platform terpercaya untuk semua kebutuhan Anda\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"w-full lg:w-1/2 flex items-center justify-center p-8 bg-white overflow-y-auto\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-md w-full space-y-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-3xl font-bold text-gray-900 mb-2\",\n            children: \"Masuk ke Akun Anda\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600\",\n            children: \"Silakan masuk untuk melanjutkan\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"w-full flex items-center justify-center px-4 py-2.5 border border-gray-300 rounded-lg shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-all duration-200\",\n            children: [/*#__PURE__*/_jsxDEV(FaGoogle, {\n              className: \"w-4 h-4 text-red-500 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 84,\n              columnNumber: 15\n            }, this), \"Masuk dengan Google\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"w-full flex items-center justify-center px-4 py-2.5 border border-gray-300 rounded-lg shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-all duration-200\",\n            children: [/*#__PURE__*/_jsxDEV(FaFacebook, {\n              className: \"w-4 h-4 text-blue-600 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 88,\n              columnNumber: 15\n            }, this), \"Masuk dengan Facebook\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-0 flex items-center\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full border-t border-gray-300\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 96,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative flex justify-center text-sm\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"px-2 bg-white text-gray-500\",\n              children: \"atau\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 99,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"email\",\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Email atau Username\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 108,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                  children: /*#__PURE__*/_jsxDEV(FaUser, {\n                    className: \"h-5 w-5 text-gray-400\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 113,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 112,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  id: \"email\",\n                  name: \"email\",\n                  type: \"text\",\n                  value: formData.email,\n                  onChange: handleInputChange,\n                  placeholder: \"Masukkan email atau username Anda\",\n                  className: \"block w-full pl-10 pr-3 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 115,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 111,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 107,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"password\",\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Password\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 130,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                  children: /*#__PURE__*/_jsxDEV(FaLock, {\n                    className: \"h-5 w-5 text-gray-400\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 135,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 134,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  id: \"password\",\n                  name: \"password\",\n                  type: showPassword ? \"text\" : \"password\",\n                  value: formData.password,\n                  onChange: handleInputChange,\n                  placeholder: \"Masukkan password Anda\",\n                  className: \"block w-full pl-10 pr-10 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 137,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  onClick: () => setShowPassword(!showPassword),\n                  className: \"absolute inset-y-0 right-0 pr-3 flex items-center\",\n                  children: showPassword ? /*#__PURE__*/_jsxDEV(FaEyeSlash, {\n                    className: \"h-5 w-5 text-gray-400 hover:text-gray-600\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 153,\n                    columnNumber: 23\n                  }, this) : /*#__PURE__*/_jsxDEV(FaEye, {\n                    className: \"h-5 w-5 text-gray-400 hover:text-gray-600\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 155,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 147,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 133,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 13\n          }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg text-sm\",\n            children: error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                id: \"remember-me\",\n                type: \"checkbox\",\n                className: \"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"remember-me\",\n                className: \"ml-2 block text-sm text-gray-600\",\n                children: \"Ingat saya\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 177,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: \"text-sm text-blue-600 hover:text-blue-500 font-medium\",\n              children: \"Lupa password?\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            disabled: loading,\n            className: `w-full flex justify-center py-2.5 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white transition-all duration-200 ${loading ? 'bg-gray-400 cursor-not-allowed' : 'bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'}`,\n            children: loading ? 'Memproses...' : 'Masuk'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-600\",\n              children: [\"Belum punya akun?\", ' ', /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                onClick: () => navigate('/register'),\n                className: \"font-medium text-blue-600 hover:text-blue-500\",\n                children: \"Daftar sekarang\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 203,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 53,\n    columnNumber: 5\n  }, this);\n};\n_s(Login, \"Hzf2giaEYJGxpTbcVY8ESXTf3sY=\", false, function () {\n  return [useNavigate];\n});\n_c = Login;\nexport default Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "FaUser", "FaLock", "FaEye", "FaEyeSlash", "FaGoogle", "FaFacebook", "authService", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "_s", "showPassword", "setShowPassword", "formData", "setFormData", "email", "password", "loading", "setLoading", "error", "setError", "navigate", "handleInputChange", "e", "name", "value", "target", "prev", "handleSubmit", "preventDefault", "response", "login", "success", "user", "data", "role", "console", "message", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "src", "alt", "onSubmit", "htmlFor", "id", "type", "onChange", "placeholder", "required", "onClick", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/JOKI MAKALAH/halobantu1/halobantu/src/features/auth/Login.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { <PERSON>a<PERSON><PERSON>, <PERSON><PERSON><PERSON>ock, <PERSON>aEye, FaEyeSlash, FaGoogle, FaFacebook } from 'react-icons/fa';\nimport authService from '../../services/authService';\n\nconst Login = () => {\n  const [showPassword, setShowPassword] = useState(false);\n  const [formData, setFormData] = useState({\n    email: '',\n    password: ''\n  });\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const navigate = useNavigate();\n\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    // Clear error when user starts typing\n    if (error) setError('');\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n\n    try {\n      const response = await authService.login(formData);\n\n      if (response.success) {\n        const user = response.data.user;\n\n        // Redirect berdasarkan role user\n        if (user.role === 'admin') {\n          navigate('/admin');\n        } else {\n          navigate('/dashboard');\n        }\n      }\n    } catch (error) {\n      console.error('Login error:', error);\n      setError(error.message || 'Login gagal. Silakan coba lagi.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"h-screen flex overflow-hidden\">\n      {/* Left Side - Image */}\n      <div className=\"hidden lg:flex lg:w-1/2 bg-gradient-to-br from-blue-600 to-purple-700 relative overflow-hidden\">\n        <div className=\"absolute inset-0 bg-black bg-opacity-20\"></div>\n        <img\n          src=\"https://images.pexels.com/photos/3184465/pexels-photo-3184465.jpeg?auto=compress&cs=tinysrgb&w=800&h=1200&fit=crop\"\n          alt=\"Login Background\"\n          className=\"w-full h-full object-cover\"\n        />\n        <div className=\"absolute inset-0 flex items-center justify-center\">\n          <div className=\"text-center text-white p-8\">\n            <h1 className=\"text-4xl font-bold mb-4\">Selamat Datang di Future X</h1>\n            <p className=\"text-xl opacity-90\">Platform terpercaya untuk semua kebutuhan Anda</p>\n          </div>\n        </div>\n      </div>\n\n      {/* Right Side - Login Form */}\n      <div className=\"w-full lg:w-1/2 flex items-center justify-center p-8 bg-white overflow-y-auto\">\n        <div className=\"max-w-md w-full space-y-6\">\n          {/* Logo dan Judul */}\n          <div className=\"text-center\">\n            <h2 className=\"text-3xl font-bold text-gray-900 mb-2\">Masuk ke Akun Anda</h2>\n            <p className=\"text-gray-600\">Silakan masuk untuk melanjutkan</p>\n          </div>\n\n\n\n          {/* Social Login Buttons */}\n          <div className=\"space-y-2\">\n            <button className=\"w-full flex items-center justify-center px-4 py-2.5 border border-gray-300 rounded-lg shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-all duration-200\">\n              <FaGoogle className=\"w-4 h-4 text-red-500 mr-2\" />\n              Masuk dengan Google\n            </button>\n            <button className=\"w-full flex items-center justify-center px-4 py-2.5 border border-gray-300 rounded-lg shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-all duration-200\">\n              <FaFacebook className=\"w-4 h-4 text-blue-600 mr-2\" />\n              Masuk dengan Facebook\n            </button>\n          </div>\n\n          {/* Divider */}\n          <div className=\"relative\">\n            <div className=\"absolute inset-0 flex items-center\">\n              <div className=\"w-full border-t border-gray-300\" />\n            </div>\n            <div className=\"relative flex justify-center text-sm\">\n              <span className=\"px-2 bg-white text-gray-500\">atau</span>\n            </div>\n          </div>\n\n          {/* Form Login */}\n          <form onSubmit={handleSubmit} className=\"space-y-4\">\n            <div className=\"space-y-3\">\n              {/* Email/Username Input */}\n              <div>\n                <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Email atau Username\n                </label>\n                <div className=\"relative\">\n                  <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                    <FaUser className=\"h-5 w-5 text-gray-400\" />\n                  </div>\n                  <input\n                    id=\"email\"\n                    name=\"email\"\n                    type=\"text\"\n                    value={formData.email}\n                    onChange={handleInputChange}\n                    placeholder=\"Masukkan email atau username Anda\"\n                    className=\"block w-full pl-10 pr-3 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200\"\n                    required\n                  />\n                </div>\n              </div>\n\n              {/* Password Input */}\n              <div>\n                <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Password\n                </label>\n                <div className=\"relative\">\n                  <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                    <FaLock className=\"h-5 w-5 text-gray-400\" />\n                  </div>\n                  <input\n                    id=\"password\"\n                    name=\"password\"\n                    type={showPassword ? \"text\" : \"password\"}\n                    value={formData.password}\n                    onChange={handleInputChange}\n                    placeholder=\"Masukkan password Anda\"\n                    className=\"block w-full pl-10 pr-10 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200\"\n                    required\n                  />\n                  <button\n                    type=\"button\"\n                    onClick={() => setShowPassword(!showPassword)}\n                    className=\"absolute inset-y-0 right-0 pr-3 flex items-center\"\n                  >\n                    {showPassword ? (\n                      <FaEyeSlash className=\"h-5 w-5 text-gray-400 hover:text-gray-600\" />\n                    ) : (\n                      <FaEye className=\"h-5 w-5 text-gray-400 hover:text-gray-600\" />\n                    )}\n                  </button>\n                </div>\n              </div>\n            </div>\n\n            {/* Error Message */}\n            {error && (\n              <div className=\"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg text-sm\">\n                {error}\n              </div>\n            )}\n\n            {/* Remember Me & Forgot Password */}\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center\">\n                <input\n                  id=\"remember-me\"\n                  type=\"checkbox\"\n                  className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                />\n                <label htmlFor=\"remember-me\" className=\"ml-2 block text-sm text-gray-600\">\n                  Ingat saya\n                </label>\n              </div>\n              <button type=\"button\" className=\"text-sm text-blue-600 hover:text-blue-500 font-medium\">\n                Lupa password?\n              </button>\n            </div>\n\n            {/* Login Button */}\n            <button\n              type=\"submit\"\n              disabled={loading}\n              className={`w-full flex justify-center py-2.5 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white transition-all duration-200 ${\n                loading\n                  ? 'bg-gray-400 cursor-not-allowed'\n                  : 'bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'\n              }`}\n            >\n              {loading ? 'Memproses...' : 'Masuk'}\n            </button>\n\n            {/* Register Link */}\n            <div className=\"text-center\">\n              <p className=\"text-sm text-gray-600\">\n                Belum punya akun?{' '}\n                <button\n                  type=\"button\"\n                  onClick={() => navigate('/register')}\n                  className=\"font-medium text-blue-600 hover:text-blue-500\"\n                >\n                  Daftar sekarang\n                </button>\n              </p>\n            </div>\n          </form>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Login; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,UAAU,QAAQ,gBAAgB;AACxF,OAAOC,WAAW,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClB,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACe,QAAQ,EAAEC,WAAW,CAAC,GAAGhB,QAAQ,CAAC;IACvCiB,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACqB,KAAK,EAAEC,QAAQ,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAMuB,QAAQ,GAAGtB,WAAW,CAAC,CAAC;EAE9B,MAAMuB,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCZ,WAAW,CAACa,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACH,IAAI,GAAGC;IACV,CAAC,CAAC,CAAC;IACH;IACA,IAAIN,KAAK,EAAEC,QAAQ,CAAC,EAAE,CAAC;EACzB,CAAC;EAED,MAAMQ,YAAY,GAAG,MAAOL,CAAC,IAAK;IAChCA,CAAC,CAACM,cAAc,CAAC,CAAC;IAClBX,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF,MAAMU,QAAQ,GAAG,MAAMxB,WAAW,CAACyB,KAAK,CAAClB,QAAQ,CAAC;MAElD,IAAIiB,QAAQ,CAACE,OAAO,EAAE;QACpB,MAAMC,IAAI,GAAGH,QAAQ,CAACI,IAAI,CAACD,IAAI;;QAE/B;QACA,IAAIA,IAAI,CAACE,IAAI,KAAK,OAAO,EAAE;UACzBd,QAAQ,CAAC,QAAQ,CAAC;QACpB,CAAC,MAAM;UACLA,QAAQ,CAAC,YAAY,CAAC;QACxB;MACF;IACF,CAAC,CAAC,OAAOF,KAAK,EAAE;MACdiB,OAAO,CAACjB,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACpCC,QAAQ,CAACD,KAAK,CAACkB,OAAO,IAAI,iCAAiC,CAAC;IAC9D,CAAC,SAAS;MACRnB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACEV,OAAA;IAAK8B,SAAS,EAAC,+BAA+B;IAAAC,QAAA,gBAE5C/B,OAAA;MAAK8B,SAAS,EAAC,gGAAgG;MAAAC,QAAA,gBAC7G/B,OAAA;QAAK8B,SAAS,EAAC;MAAyC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC/DnC,OAAA;QACEoC,GAAG,EAAC,oHAAoH;QACxHC,GAAG,EAAC,kBAAkB;QACtBP,SAAS,EAAC;MAA4B;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC,eACFnC,OAAA;QAAK8B,SAAS,EAAC,mDAAmD;QAAAC,QAAA,eAChE/B,OAAA;UAAK8B,SAAS,EAAC,4BAA4B;UAAAC,QAAA,gBACzC/B,OAAA;YAAI8B,SAAS,EAAC,yBAAyB;YAAAC,QAAA,EAAC;UAA0B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvEnC,OAAA;YAAG8B,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAA8C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnC,OAAA;MAAK8B,SAAS,EAAC,+EAA+E;MAAAC,QAAA,eAC5F/B,OAAA;QAAK8B,SAAS,EAAC,2BAA2B;QAAAC,QAAA,gBAExC/B,OAAA;UAAK8B,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B/B,OAAA;YAAI8B,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EAAC;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7EnC,OAAA;YAAG8B,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAA+B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC,eAKNnC,OAAA;UAAK8B,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB/B,OAAA;YAAQ8B,SAAS,EAAC,yLAAyL;YAAAC,QAAA,gBACzM/B,OAAA,CAACJ,QAAQ;cAACkC,SAAS,EAAC;YAA2B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,uBAEpD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTnC,OAAA;YAAQ8B,SAAS,EAAC,yLAAyL;YAAAC,QAAA,gBACzM/B,OAAA,CAACH,UAAU;cAACiC,SAAS,EAAC;YAA4B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,yBAEvD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGNnC,OAAA;UAAK8B,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvB/B,OAAA;YAAK8B,SAAS,EAAC,oCAAoC;YAAAC,QAAA,eACjD/B,OAAA;cAAK8B,SAAS,EAAC;YAAiC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC,eACNnC,OAAA;YAAK8B,SAAS,EAAC,sCAAsC;YAAAC,QAAA,eACnD/B,OAAA;cAAM8B,SAAS,EAAC,6BAA6B;cAAAC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNnC,OAAA;UAAMsC,QAAQ,EAAElB,YAAa;UAACU,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACjD/B,OAAA;YAAK8B,SAAS,EAAC,WAAW;YAAAC,QAAA,gBAExB/B,OAAA;cAAA+B,QAAA,gBACE/B,OAAA;gBAAOuC,OAAO,EAAC,OAAO;gBAACT,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRnC,OAAA;gBAAK8B,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACvB/B,OAAA;kBAAK8B,SAAS,EAAC,sEAAsE;kBAAAC,QAAA,eACnF/B,OAAA,CAACR,MAAM;oBAACsC,SAAS,EAAC;kBAAuB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzC,CAAC,eACNnC,OAAA;kBACEwC,EAAE,EAAC,OAAO;kBACVxB,IAAI,EAAC,OAAO;kBACZyB,IAAI,EAAC,MAAM;kBACXxB,KAAK,EAAEZ,QAAQ,CAACE,KAAM;kBACtBmC,QAAQ,EAAE5B,iBAAkB;kBAC5B6B,WAAW,EAAC,mCAAmC;kBAC/Cb,SAAS,EAAC,2KAA2K;kBACrLc,QAAQ;gBAAA;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNnC,OAAA;cAAA+B,QAAA,gBACE/B,OAAA;gBAAOuC,OAAO,EAAC,UAAU;gBAACT,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEnF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRnC,OAAA;gBAAK8B,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACvB/B,OAAA;kBAAK8B,SAAS,EAAC,sEAAsE;kBAAAC,QAAA,eACnF/B,OAAA,CAACP,MAAM;oBAACqC,SAAS,EAAC;kBAAuB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzC,CAAC,eACNnC,OAAA;kBACEwC,EAAE,EAAC,UAAU;kBACbxB,IAAI,EAAC,UAAU;kBACfyB,IAAI,EAAEtC,YAAY,GAAG,MAAM,GAAG,UAAW;kBACzCc,KAAK,EAAEZ,QAAQ,CAACG,QAAS;kBACzBkC,QAAQ,EAAE5B,iBAAkB;kBAC5B6B,WAAW,EAAC,wBAAwB;kBACpCb,SAAS,EAAC,4KAA4K;kBACtLc,QAAQ;gBAAA;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC,eACFnC,OAAA;kBACEyC,IAAI,EAAC,QAAQ;kBACbI,OAAO,EAAEA,CAAA,KAAMzC,eAAe,CAAC,CAACD,YAAY,CAAE;kBAC9C2B,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,EAE5D5B,YAAY,gBACXH,OAAA,CAACL,UAAU;oBAACmC,SAAS,EAAC;kBAA2C;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAEpEnC,OAAA,CAACN,KAAK;oBAACoC,SAAS,EAAC;kBAA2C;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAC/D;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGLxB,KAAK,iBACJX,OAAA;YAAK8B,SAAS,EAAC,2EAA2E;YAAAC,QAAA,EACvFpB;UAAK;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eAGDnC,OAAA;YAAK8B,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChD/B,OAAA;cAAK8B,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChC/B,OAAA;gBACEwC,EAAE,EAAC,aAAa;gBAChBC,IAAI,EAAC,UAAU;gBACfX,SAAS,EAAC;cAAmE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9E,CAAC,eACFnC,OAAA;gBAAOuC,OAAO,EAAC,aAAa;gBAACT,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAAC;cAE1E;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACNnC,OAAA;cAAQyC,IAAI,EAAC,QAAQ;cAACX,SAAS,EAAC,uDAAuD;cAAAC,QAAA,EAAC;YAExF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGNnC,OAAA;YACEyC,IAAI,EAAC,QAAQ;YACbK,QAAQ,EAAErC,OAAQ;YAClBqB,SAAS,EAAE,oJACTrB,OAAO,GACH,gCAAgC,GAChC,uGAAuG,EAC1G;YAAAsB,QAAA,EAEFtB,OAAO,GAAG,cAAc,GAAG;UAAO;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eAGTnC,OAAA;YAAK8B,SAAS,EAAC,aAAa;YAAAC,QAAA,eAC1B/B,OAAA;cAAG8B,SAAS,EAAC,uBAAuB;cAAAC,QAAA,GAAC,mBAClB,EAAC,GAAG,eACrB/B,OAAA;gBACEyC,IAAI,EAAC,QAAQ;gBACbI,OAAO,EAAEA,CAAA,KAAMhC,QAAQ,CAAC,WAAW,CAAE;gBACrCiB,SAAS,EAAC,+CAA+C;gBAAAC,QAAA,EAC1D;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjC,EAAA,CAnNID,KAAK;EAAA,QAQQV,WAAW;AAAA;AAAAwD,EAAA,GARxB9C,KAAK;AAqNX,eAAeA,KAAK;AAAC,IAAA8C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}