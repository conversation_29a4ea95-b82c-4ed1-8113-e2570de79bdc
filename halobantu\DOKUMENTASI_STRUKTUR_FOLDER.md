# 📁 Dokumentasi Struktur Folder Proyek HaloBantu

## 🎯 Gambaran Umum
Proyek HaloBantu adalah aplikasi web berbasis React yang menyediakan layanan konsultasi psikologi dan workshop. Struktur folder dirancang mengikuti best practices untuk proyek React dengan organisasi yang jelas berdasarkan fitur dan fungsi.

---

## 📂 Struktur Folder Utama

### 🔧 **Folder Konfigurasi & Dependencies**

#### `node_modules/`
- **Kegunaan**: Menyimpan semua package dan library yang diinstall melalui npm
- **Isi**: Dependencies seperti React, React Router, Tailwind CSS, dan library lainnya
- **Catatan**: Folder ini otomatis dibuat saat menjalankan `npm install`

#### `public/`
- **Kegunaan**: Menyimpan file statis yang dapat diakses langsung oleh browser
- **Isi**:
  - `index.html` - Template HTML utama
  - `favicon.ico` - Icon website
  - `logo192.png`, `logo512.png` - Logo aplikasi
  - `manifest.json` - Konfigurasi PWA (Progressive Web App)
  - `robots.txt` - Instruksi untuk search engine crawler

#### `api/`
- **Kegunaan**: Folder untuk backend API (saat ini kosong)
- **Rencana**: Akan berisi kode server-side untuk menangani request dari frontend

---

### 🎨 **Folder Source Code Utama (`src/`)**

#### `src/App.js`
- **Kegunaan**: Komponen utama aplikasi yang mengatur routing dan layout global
- **Fungsi**: Entry point untuk semua komponen React

#### `src/index.js`
- **Kegunaan**: File entry point yang me-render aplikasi React ke DOM
- **Fungsi**: Menghubungkan React dengan HTML

---

### 🧩 **Folder Components (`src/components/`)**

#### `src/components/common/`
- **Kegunaan**: Komponen yang dapat digunakan di berbagai bagian aplikasi
- **Contoh**: Button, Modal, Loading spinner, dll
- **Prinsip**: Reusable dan tidak terikat pada fitur tertentu

#### `src/components/features/`
- **Kegunaan**: Komponen khusus untuk fitur tertentu
- **Struktur**:
  - `psychology/` - Komponen khusus fitur psikologi
- **Prinsip**: Komponen yang spesifik untuk satu fitur

#### `src/components/forms/`
- **Kegunaan**: Komponen form yang dapat digunakan kembali
- **Contoh**: Input field, validation, form wrapper
- **Manfaat**: Konsistensi tampilan dan validasi form

#### `src/components/layout/`
- **Kegunaan**: Komponen untuk struktur layout aplikasi
- **Isi**:
  - `Header.js` - Navigation bar dan menu utama
  - `Footer.js` - Footer dengan informasi kontak dan link
  - `Layout.js` - Wrapper layout yang menggabungkan header dan footer
- **Fungsi**: Memberikan struktur konsisten di seluruh aplikasi

#### `src/components/ui/`
- **Kegunaan**: Komponen UI dasar dan reusable
- **Isi**:
  - `BackButton.js` - Tombol kembali
  - `FeatureCard.js` - Card untuk menampilkan fitur
- **Prinsip**: Komponen UI yang dapat digunakan di mana saja

---

### 🚀 **Folder Features (`src/features/`)**
Organisasi berdasarkan fitur aplikasi - setiap folder berisi semua yang dibutuhkan untuk satu fitur.

#### `src/features/admin/`
- **Kegunaan**: Fitur khusus untuk administrator
- **Isi**: `AdminDashboard.js` - Dashboard admin
- **Fungsi**: Mengelola sistem, user, dan data aplikasi

#### `src/features/auth/`
- **Kegunaan**: Fitur autentikasi dan otorisasi
- **Isi**: `Login.js` - Halaman login
- **Fungsi**: Login, register, logout, forgot password

#### `src/features/daily-services/`
- **Kegunaan**: Fitur layanan harian
- **Isi**: `DailyServicesPage.js` - Halaman layanan harian
- **Fungsi**: Menampilkan dan mengelola layanan yang tersedia setiap hari

#### `src/features/dashboard/`
- **Kegunaan**: Dashboard utama pengguna
- **Isi**: 
  - `Dashboard.js` - Komponen dashboard utama
  - `components/` - Komponen khusus dashboard
- **Fungsi**: Menampilkan ringkasan dan akses cepat ke fitur

#### `src/features/psychology/`
- **Kegunaan**: Fitur konsultasi psikologi
- **Struktur**:
  - `components/` - Komponen khusus psikologi
  - `pages/` - Halaman-halaman dalam fitur psikologi
- **Fungsi**: Booking konsultasi, chat dengan psikolog, riwayat konsultasi

#### `src/features/workshop/`
- **Kegunaan**: Fitur workshop dan pelatihan
- **Isi**: `WorkshopPage.js` - Halaman workshop
- **Fungsi**: Pendaftaran workshop, materi, jadwal

---

### 📄 **Folder Pages (`src/pages/`)**
Organisasi halaman berdasarkan area aplikasi.

#### `src/pages/admin/`
- **Kegunaan**: Halaman-halaman untuk admin
- **Fungsi**: Manajemen user, content, sistem

#### `src/pages/auth/`
- **Kegunaan**: Halaman autentikasi
- **Fungsi**: Login, register, forgot password

#### `src/pages/daily-services/`
- **Kegunaan**: Halaman layanan harian
- **Fungsi**: Menampilkan layanan yang tersedia

#### `src/pages/psychology/`
- **Kegunaan**: Halaman konsultasi psikologi
- **Fungsi**: Booking, konsultasi, riwayat

#### `src/pages/user/`
- **Kegunaan**: Halaman khusus user biasa
- **Struktur**:
  - `dashboard/` - Dashboard user
  - `psychology/` - Halaman psikologi untuk user
- **Fungsi**: Profil, pengaturan, riwayat

#### `src/pages/workshop/`
- **Kegunaan**: Halaman workshop
- **Fungsi**: Daftar workshop, detail, pendaftaran

---

### 🛠️ **Folder Utilitas & Konfigurasi**

#### `src/assets/`
- **Kegunaan**: File statis seperti gambar, icon, font
- **Isi**: `logo.svg` - Logo aplikasi
- **Manfaat**: Organisasi file media yang rapi

#### `src/constants/`
- **Kegunaan**: Menyimpan konstanta dan konfigurasi aplikasi
- **Contoh**: API endpoints, status codes, default values
- **Manfaat**: Sentralisasi konfigurasi

#### `src/context/`
- **Kegunaan**: React Context untuk state management global
- **Fungsi**: Berbagi state antar komponen tanpa prop drilling
- **Contoh**: User context, theme context

#### `src/hooks/`
- **Kegunaan**: Custom React hooks
- **Fungsi**: Logic yang dapat digunakan kembali
- **Contoh**: useAuth, useApi, useLocalStorage

#### `src/services/`
- **Kegunaan**: Service layer untuk komunikasi dengan API
- **Fungsi**: HTTP requests, data transformation
- **Manfaat**: Pemisahan logic API dari komponen

#### `src/styles/`
- **Kegunaan**: File CSS dan styling
- **Isi**:
  - `App.css` - Style untuk komponen App
  - `index.css` - Global styles dan Tailwind imports
- **Fungsi**: Styling aplikasi

#### `src/utils/`
- **Kegunaan**: Utility functions dan helper
- **Contoh**: Format date, validation, calculations
- **Manfaat**: Reusable functions

---

### 📋 **File Konfigurasi Root**

#### `package.json`
- **Kegunaan**: Konfigurasi npm, dependencies, dan scripts
- **Isi**: Dependencies, devDependencies, scripts untuk build/start

#### `package-lock.json`
- **Kegunaan**: Lock file untuk memastikan versi dependencies konsisten
- **Fungsi**: Reproducible builds

#### `tailwind.config.js`
- **Kegunaan**: Konfigurasi Tailwind CSS
- **Fungsi**: Custom colors, spacing, breakpoints

#### `postcss.config.js`
- **Kegunaan**: Konfigurasi PostCSS untuk processing CSS
- **Fungsi**: Autoprefixer, Tailwind processing

#### `README.md`
- **Kegunaan**: Dokumentasi proyek
- **Isi**: Cara install, run, dan develop aplikasi

#### `create_folders.ps1`
- **Kegunaan**: Script PowerShell untuk membuat struktur folder
- **Fungsi**: Otomasi setup struktur proyek

---

## 🎯 **Prinsip Organisasi**

### 1. **Feature-Based Structure**
- Setiap fitur memiliki folder sendiri dengan semua komponen terkait
- Memudahkan maintenance dan development

### 2. **Separation of Concerns**
- Components: UI dan presentasi
- Services: Business logic dan API calls
- Utils: Helper functions
- Constants: Konfigurasi

### 3. **Reusability**
- Common components dapat digunakan di berbagai fitur
- Custom hooks untuk logic yang dapat digunakan kembali

### 4. **Scalability**
- Struktur yang mudah diperluas saat aplikasi berkembang
- Clear boundaries antar modul

---

## 📝 **Tips Penggunaan**

1. **Menambah Fitur Baru**: Buat folder di `features/` dengan struktur lengkap
2. **Komponen Reusable**: Letakkan di `components/common/` atau `components/ui/`
3. **API Integration**: Gunakan folder `services/` untuk semua API calls
4. **Styling**: Gunakan Tailwind classes, custom CSS di `styles/`
5. **State Management**: Gunakan Context API di folder `context/`

Struktur ini dirancang untuk mendukung pengembangan aplikasi yang scalable, maintainable, dan mudah dipahami oleh tim developer.
