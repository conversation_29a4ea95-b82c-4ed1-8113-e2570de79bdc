{"ast": null, "code": "import api from './api';\n\n// Auth service untuk komunikasi dengan backend\nexport const authService = {\n  // <PERSON>gin dengan email/username dan password\n  login: async credentials => {\n    try {\n      console.log('🔄 Attempting login with:', credentials);\n      const response = await api.post('/auth/login', credentials);\n      console.log('✅ Login response:', response.data);\n\n      // Simpan token dan user data ke localStorage\n      if (response.data.success) {\n        const {\n          token,\n          refreshToken,\n          user\n        } = response.data.data;\n        console.log('👤 User data:', user);\n        console.log('🔑 Token:', token);\n        localStorage.setItem('token', token);\n        localStorage.setItem('refreshToken', refreshToken);\n        localStorage.setItem('user', JSON.stringify(user));\n        console.log('💾 Data saved to localStorage');\n      }\n      return response.data;\n    } catch (error) {\n      var _error$response;\n      console.error('❌ Login error:', error);\n      throw ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.data) || {\n        message: 'Login failed'\n      };\n    }\n  },\n  // Register user baru\n  register: async userData => {\n    try {\n      const response = await api.post('/auth/register', userData);\n\n      // Simpan token dan user data ke localStorage setelah register\n      if (response.data.success) {\n        const {\n          token,\n          refreshToken,\n          user\n        } = response.data.data;\n        localStorage.setItem('token', token);\n        localStorage.setItem('refreshToken', refreshToken);\n        localStorage.setItem('user', JSON.stringify(user));\n      }\n      return response.data;\n    } catch (error) {\n      var _error$response2;\n      throw ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.data) || {\n        message: 'Registration failed'\n      };\n    }\n  },\n  // Logout\n  logout: async () => {\n    try {\n      await api.post('/auth/logout');\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      // Hapus data dari localStorage\n      localStorage.removeItem('token');\n      localStorage.removeItem('refreshToken');\n      localStorage.removeItem('user');\n    }\n  },\n  // Get user profile\n  getProfile: async () => {\n    try {\n      const response = await api.get('/auth/profile');\n      return response.data;\n    } catch (error) {\n      var _error$response3;\n      throw ((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : _error$response3.data) || {\n        message: 'Failed to get profile'\n      };\n    }\n  },\n  // Check if user is logged in\n  isAuthenticated: () => {\n    const token = localStorage.getItem('token');\n    const user = localStorage.getItem('user');\n    return !!(token && user);\n  },\n  // Get current user from localStorage\n  getCurrentUser: () => {\n    const user = localStorage.getItem('user');\n    return user ? JSON.parse(user) : null;\n  },\n  // Get token from localStorage\n  getToken: () => {\n    return localStorage.getItem('token');\n  }\n};\nexport default authService;", "map": {"version": 3, "names": ["api", "authService", "login", "credentials", "console", "log", "response", "post", "data", "success", "token", "refreshToken", "user", "localStorage", "setItem", "JSON", "stringify", "error", "_error$response", "message", "register", "userData", "_error$response2", "logout", "removeItem", "getProfile", "get", "_error$response3", "isAuthenticated", "getItem", "getCurrentUser", "parse", "getToken"], "sources": ["C:/Users/<USER>/Documents/JOKI MAKALAH/halobantu1/halobantu/src/services/authService.js"], "sourcesContent": ["import api from './api';\n\n// Auth service untuk komunikasi dengan backend\nexport const authService = {\n  // <PERSON>gin dengan email/username dan password\n  login: async (credentials) => {\n    try {\n      console.log('🔄 Attempting login with:', credentials);\n      const response = await api.post('/auth/login', credentials);\n      console.log('✅ Login response:', response.data);\n\n      // Simpan token dan user data ke localStorage\n      if (response.data.success) {\n        const { token, refreshToken, user } = response.data.data;\n        console.log('👤 User data:', user);\n        console.log('🔑 Token:', token);\n\n        localStorage.setItem('token', token);\n        localStorage.setItem('refreshToken', refreshToken);\n        localStorage.setItem('user', JSON.stringify(user));\n\n        console.log('💾 Data saved to localStorage');\n      }\n\n      return response.data;\n    } catch (error) {\n      console.error('❌ Login error:', error);\n      throw error.response?.data || { message: '<PERSON><PERSON> failed' };\n    }\n  },\n\n  // Register user baru\n  register: async (userData) => {\n    try {\n      const response = await api.post('/auth/register', userData);\n      \n      // Simpan token dan user data ke localStorage setelah register\n      if (response.data.success) {\n        const { token, refreshToken, user } = response.data.data;\n        localStorage.setItem('token', token);\n        localStorage.setItem('refreshToken', refreshToken);\n        localStorage.setItem('user', JSON.stringify(user));\n      }\n      \n      return response.data;\n    } catch (error) {\n      throw error.response?.data || { message: 'Registration failed' };\n    }\n  },\n\n  // Logout\n  logout: async () => {\n    try {\n      await api.post('/auth/logout');\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      // Hapus data dari localStorage\n      localStorage.removeItem('token');\n      localStorage.removeItem('refreshToken');\n      localStorage.removeItem('user');\n    }\n  },\n\n  // Get user profile\n  getProfile: async () => {\n    try {\n      const response = await api.get('/auth/profile');\n      return response.data;\n    } catch (error) {\n      throw error.response?.data || { message: 'Failed to get profile' };\n    }\n  },\n\n  // Check if user is logged in\n  isAuthenticated: () => {\n    const token = localStorage.getItem('token');\n    const user = localStorage.getItem('user');\n    return !!(token && user);\n  },\n\n  // Get current user from localStorage\n  getCurrentUser: () => {\n    const user = localStorage.getItem('user');\n    return user ? JSON.parse(user) : null;\n  },\n\n  // Get token from localStorage\n  getToken: () => {\n    return localStorage.getItem('token');\n  }\n};\n\nexport default authService;\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,OAAO;;AAEvB;AACA,OAAO,MAAMC,WAAW,GAAG;EACzB;EACAC,KAAK,EAAE,MAAOC,WAAW,IAAK;IAC5B,IAAI;MACFC,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEF,WAAW,CAAC;MACrD,MAAMG,QAAQ,GAAG,MAAMN,GAAG,CAACO,IAAI,CAAC,aAAa,EAAEJ,WAAW,CAAC;MAC3DC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEC,QAAQ,CAACE,IAAI,CAAC;;MAE/C;MACA,IAAIF,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzB,MAAM;UAAEC,KAAK;UAAEC,YAAY;UAAEC;QAAK,CAAC,GAAGN,QAAQ,CAACE,IAAI,CAACA,IAAI;QACxDJ,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEO,IAAI,CAAC;QAClCR,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEK,KAAK,CAAC;QAE/BG,YAAY,CAACC,OAAO,CAAC,OAAO,EAAEJ,KAAK,CAAC;QACpCG,YAAY,CAACC,OAAO,CAAC,cAAc,EAAEH,YAAY,CAAC;QAClDE,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACJ,IAAI,CAAC,CAAC;QAElDR,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;MAC9C;MAEA,OAAOC,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOS,KAAK,EAAE;MAAA,IAAAC,eAAA;MACdd,OAAO,CAACa,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;MACtC,MAAM,EAAAC,eAAA,GAAAD,KAAK,CAACX,QAAQ,cAAAY,eAAA,uBAAdA,eAAA,CAAgBV,IAAI,KAAI;QAAEW,OAAO,EAAE;MAAe,CAAC;IAC3D;EACF,CAAC;EAED;EACAC,QAAQ,EAAE,MAAOC,QAAQ,IAAK;IAC5B,IAAI;MACF,MAAMf,QAAQ,GAAG,MAAMN,GAAG,CAACO,IAAI,CAAC,gBAAgB,EAAEc,QAAQ,CAAC;;MAE3D;MACA,IAAIf,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzB,MAAM;UAAEC,KAAK;UAAEC,YAAY;UAAEC;QAAK,CAAC,GAAGN,QAAQ,CAACE,IAAI,CAACA,IAAI;QACxDK,YAAY,CAACC,OAAO,CAAC,OAAO,EAAEJ,KAAK,CAAC;QACpCG,YAAY,CAACC,OAAO,CAAC,cAAc,EAAEH,YAAY,CAAC;QAClDE,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACJ,IAAI,CAAC,CAAC;MACpD;MAEA,OAAON,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOS,KAAK,EAAE;MAAA,IAAAK,gBAAA;MACd,MAAM,EAAAA,gBAAA,GAAAL,KAAK,CAACX,QAAQ,cAAAgB,gBAAA,uBAAdA,gBAAA,CAAgBd,IAAI,KAAI;QAAEW,OAAO,EAAE;MAAsB,CAAC;IAClE;EACF,CAAC;EAED;EACAI,MAAM,EAAE,MAAAA,CAAA,KAAY;IAClB,IAAI;MACF,MAAMvB,GAAG,CAACO,IAAI,CAAC,cAAc,CAAC;IAChC,CAAC,CAAC,OAAOU,KAAK,EAAE;MACdb,OAAO,CAACa,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;IACvC,CAAC,SAAS;MACR;MACAJ,YAAY,CAACW,UAAU,CAAC,OAAO,CAAC;MAChCX,YAAY,CAACW,UAAU,CAAC,cAAc,CAAC;MACvCX,YAAY,CAACW,UAAU,CAAC,MAAM,CAAC;IACjC;EACF,CAAC;EAED;EACAC,UAAU,EAAE,MAAAA,CAAA,KAAY;IACtB,IAAI;MACF,MAAMnB,QAAQ,GAAG,MAAMN,GAAG,CAAC0B,GAAG,CAAC,eAAe,CAAC;MAC/C,OAAOpB,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOS,KAAK,EAAE;MAAA,IAAAU,gBAAA;MACd,MAAM,EAAAA,gBAAA,GAAAV,KAAK,CAACX,QAAQ,cAAAqB,gBAAA,uBAAdA,gBAAA,CAAgBnB,IAAI,KAAI;QAAEW,OAAO,EAAE;MAAwB,CAAC;IACpE;EACF,CAAC;EAED;EACAS,eAAe,EAAEA,CAAA,KAAM;IACrB,MAAMlB,KAAK,GAAGG,YAAY,CAACgB,OAAO,CAAC,OAAO,CAAC;IAC3C,MAAMjB,IAAI,GAAGC,YAAY,CAACgB,OAAO,CAAC,MAAM,CAAC;IACzC,OAAO,CAAC,EAAEnB,KAAK,IAAIE,IAAI,CAAC;EAC1B,CAAC;EAED;EACAkB,cAAc,EAAEA,CAAA,KAAM;IACpB,MAAMlB,IAAI,GAAGC,YAAY,CAACgB,OAAO,CAAC,MAAM,CAAC;IACzC,OAAOjB,IAAI,GAAGG,IAAI,CAACgB,KAAK,CAACnB,IAAI,CAAC,GAAG,IAAI;EACvC,CAAC;EAED;EACAoB,QAAQ,EAAEA,CAAA,KAAM;IACd,OAAOnB,YAAY,CAACgB,OAAO,CAAC,OAAO,CAAC;EACtC;AACF,CAAC;AAED,eAAe5B,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}