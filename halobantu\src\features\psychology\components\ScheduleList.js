import React from 'react';
import { FaClock, FaCalendarAlt, FaCheck, FaTimes } from 'react-icons/fa';

const ScheduleList = ({ schedules, onSelect }) => {
  // Fungsi untuk mengelompokkan jadwal berdasarkan hari
  const groupSchedulesByDay = (schedules) => {
    return schedules.reduce((acc, schedule) => {
      const day = schedule.day;
      if (!acc[day]) {
        acc[day] = [];
      }
      acc[day].push(schedule);
      return acc;
    }, {});
  };

  const groupedSchedules = groupSchedulesByDay(schedules);

  return (
    <div className="bg-white rounded-xl shadow-lg p-6">
      <h3 className="text-xl font-bold text-gray-800 mb-4">Jadwal Konsultasi</h3>
      
      {Object.entries(groupedSchedules).map(([day, daySchedules]) => (
        <div key={day} className="mb-6 last:mb-0">
          <h4 className="text-lg font-semibold text-gray-700 mb-3 flex items-center">
            <FaCalendarAlt className="mr-2 text-blue-500" />
            {day}
          </h4>
          
          <div className="space-y-2">
            {daySchedules.map((schedule) => (
              <div
                key={schedule.id}
                className={`
                  flex items-center justify-between p-3 rounded-lg border
                  ${schedule.isAvailable 
                    ? 'border-gray-200 hover:border-blue-500 cursor-pointer' 
                    : 'border-gray-100 bg-gray-50'
                  }
                  transition-all duration-200
                `}
                onClick={() => schedule.isAvailable && onSelect(schedule)}
              >
                <div className="flex items-center space-x-3">
                  <FaClock className={`${schedule.isAvailable ? 'text-blue-500' : 'text-gray-400'}`} />
                  <span className={`${schedule.isAvailable ? 'text-gray-700' : 'text-gray-400'}`}>
                    {schedule.time}
                  </span>
                </div>
                
                <div className="flex items-center space-x-2">
                  {schedule.isAvailable ? (
                    <>
                      <span className="text-green-500 text-sm">Tersedia</span>
                      <FaCheck className="text-green-500" />
                    </>
                  ) : (
                    <>
                      <span className="text-red-500 text-sm">Terisi</span>
                      <FaTimes className="text-red-500" />
                    </>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      ))}
    </div>
  );
};

export default ScheduleList; 