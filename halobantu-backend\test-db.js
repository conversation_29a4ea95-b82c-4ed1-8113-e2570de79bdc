require('dotenv').config();
const { sequelize } = require('./config/database');

async function testConnection() {
  try {
    await sequelize.authenticate();
    console.log('✅ Database connection established successfully.');
    
    // Test query
    const [results] = await sequelize.query('SELECT COUNT(*) as count FROM users');
    console.log('✅ Users table accessible. Total users:', results[0].count);
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Unable to connect to the database:', error.message);
    console.error('❌ Full error:', error);
    process.exit(1);
  }
}

testConnection();
