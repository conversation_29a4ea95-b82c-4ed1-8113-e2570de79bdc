{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\JOKI MAKALAH\\\\halobantu1\\\\halobantu\\\\src\\\\features\\\\dashboard\\\\components\\\\WhyUsSection.js\";\nimport React from 'react';\nimport { FaShieldAlt, FaRocket, FaHeart, FaStar, FaHandshake, FaCog } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst WhyUsSection = () => {\n  const reasons = [{\n    icon: /*#__PURE__*/_jsxDEV(FaShieldAlt, {\n      className: \"text-4xl text-blue-600\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 13\n    }, this),\n    title: \"Teknologi Terdepan\",\n    description: \"Menggunakan teknologi canggih dan inovasi terbaru untuk memberikan solusi otomotif masa depan.\"\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(FaRocket, {\n      className: \"text-4xl text-purple-600\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 13\n    }, this),\n    title: \"Performa Optimal\",\n    description: \"Layanan berkualitas tinggi dengan standar internasional untuk hasil yang maksimal dan memuaskan.\"\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(FaHeart, {\n      className: \"text-4xl text-red-600\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 13\n    }, this),\n    title: \"Pelayanan Premium\",\n    description: \"Tim profesional berpengalaman siap memberikan layanan terbaik dengan pendekatan personal.\"\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(FaStar, {\n      className: \"text-4xl text-yellow-600\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 13\n    }, this),\n    title: \"Standar Internasional\",\n    description: \"Mengikuti standar kualitas internasional dengan sertifikasi dan jaminan kualitas terbaik.\"\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(FaHandshake, {\n      className: \"text-4xl text-green-600\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 13\n    }, this),\n    title: \"Kepercayaan Tinggi\",\n    description: \"Dipercaya oleh ribuan pelanggan dengan track record yang terbukti dan testimoni positif.\"\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(FaCog, {\n      className: \"text-4xl text-gray-600\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 13\n    }, this),\n    title: \"Inovasi Berkelanjutan\",\n    description: \"Terus berinovasi dan mengembangkan teknologi untuk memberikan solusi terbaik di masa depan.\"\n  }];\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"py-20 bg-gray-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-16\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-4xl md:text-5xl font-bold text-gray-800 mb-4\",\n          children: \"Kenapa Kami?\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n          children: \"Future X menghadirkan revolusi teknologi otomotif dengan standar kualitas internasional dan inovasi berkelanjutan\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto\",\n        children: reasons.map((reason, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 group\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"inline-flex items-center justify-center w-16 h-16 bg-gray-50 rounded-full mb-6 group-hover:scale-110 transition-transform duration-300\",\n              children: reason.icon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 59,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-bold text-gray-800 mb-4\",\n              children: reason.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 62,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 leading-relaxed\",\n              children: reason.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 65,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 15\n          }, this)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mt-16\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-2xl p-8 shadow-lg max-w-4xl mx-auto\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-3xl font-bold text-gray-800 mb-4\",\n            children: \"Siap Memasuki Era Future X?\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-lg text-gray-600 mb-8\",\n            children: \"Bergabunglah dengan revolusi teknologi otomotif dan rasakan pengalaman masa depan hari ini\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => document.getElementById('services').scrollIntoView({\n                behavior: 'smooth'\n              }),\n              className: \"px-8 py-4 bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-lg transition-all duration-300 transform hover:scale-105\",\n              children: \"Mulai Sekarang\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 83,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => document.getElementById('contact').scrollIntoView({\n                behavior: 'smooth'\n              }),\n              className: \"px-8 py-4 bg-gray-100 hover:bg-gray-200 text-gray-800 font-semibold rounded-lg transition-all duration-300\",\n              children: \"Hubungi Kami\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 89,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 39,\n    columnNumber: 5\n  }, this);\n};\n_c = WhyUsSection;\nexport default WhyUsSection;\nvar _c;\n$RefreshReg$(_c, \"WhyUsSection\");", "map": {"version": 3, "names": ["React", "FaShieldAlt", "FaRocket", "FaHeart", "FaStar", "FaHandshake", "FaCog", "jsxDEV", "_jsxDEV", "WhyUsSection", "reasons", "icon", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "title", "description", "children", "map", "reason", "index", "onClick", "document", "getElementById", "scrollIntoView", "behavior", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/JOKI MAKALAH/halobantu1/halobantu/src/features/dashboard/components/WhyUsSection.js"], "sourcesContent": ["import React from 'react';\nimport { FaShieldAlt, FaRocket, FaHeart, FaStar, FaHandshake, FaCog } from 'react-icons/fa';\n\nconst WhyUsSection = () => {\n  const reasons = [\n    {\n      icon: <FaShieldAlt className=\"text-4xl text-blue-600\" />,\n      title: \"Teknologi Terdepan\",\n      description: \"Menggunakan teknologi canggih dan inovasi terbaru untuk memberikan solusi otomotif masa depan.\"\n    },\n    {\n      icon: <FaRocket className=\"text-4xl text-purple-600\" />,\n      title: \"Performa Optimal\",\n      description: \"Layanan berkualitas tinggi dengan standar internasional untuk hasil yang maksimal dan memuaskan.\"\n    },\n    {\n      icon: <FaHeart className=\"text-4xl text-red-600\" />,\n      title: \"Pelayanan Premium\",\n      description: \"Tim profesional berpengalaman siap memberikan layanan terbaik dengan pendekatan personal.\"\n    },\n    {\n      icon: <FaStar className=\"text-4xl text-yellow-600\" />,\n      title: \"Standar Internasional\",\n      description: \"Mengikuti standar kualitas internasional dengan sertifikasi dan jaminan kualitas terbaik.\"\n    },\n    {\n      icon: <FaHandshake className=\"text-4xl text-green-600\" />,\n      title: \"Kepercayaan Tinggi\",\n      description: \"Dipercaya oleh ribuan pelanggan dengan track record yang terbukti dan testimoni positif.\"\n    },\n    {\n      icon: <FaCog className=\"text-4xl text-gray-600\" />,\n      title: \"Inovasi Berkelanjutan\",\n      description: \"Terus berinovasi dan mengembangkan teknologi untuk memberikan solusi terbaik di masa depan.\"\n    }\n  ];\n\n  return (\n    <section className=\"py-20 bg-gray-50\">\n      <div className=\"container mx-auto px-4\">\n        {/* Section Header */}\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-4xl md:text-5xl font-bold text-gray-800 mb-4\">\n            Kenapa Kami?\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            Future X menghadirkan revolusi teknologi otomotif dengan standar kualitas internasional dan inovasi berkelanjutan\n          </p>\n        </div>\n\n        {/* Reasons Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto\">\n          {reasons.map((reason, index) => (\n            <div\n              key={index}\n              className=\"bg-white rounded-xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 group\"\n            >\n              <div className=\"text-center\">\n                <div className=\"inline-flex items-center justify-center w-16 h-16 bg-gray-50 rounded-full mb-6 group-hover:scale-110 transition-transform duration-300\">\n                  {reason.icon}\n                </div>\n                <h3 className=\"text-xl font-bold text-gray-800 mb-4\">\n                  {reason.title}\n                </h3>\n                <p className=\"text-gray-600 leading-relaxed\">\n                  {reason.description}\n                </p>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* CTA Section */}\n        <div className=\"text-center mt-16\">\n          <div className=\"bg-white rounded-2xl p-8 shadow-lg max-w-4xl mx-auto\">\n            <h3 className=\"text-3xl font-bold text-gray-800 mb-4\">\n              Siap Memasuki Era Future X?\n            </h3>\n            <p className=\"text-lg text-gray-600 mb-8\">\n              Bergabunglah dengan revolusi teknologi otomotif dan rasakan pengalaman masa depan hari ini\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <button \n                onClick={() => document.getElementById('services').scrollIntoView({ behavior: 'smooth' })}\n                className=\"px-8 py-4 bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-lg transition-all duration-300 transform hover:scale-105\"\n              >\n                Mulai Sekarang\n              </button>\n              <button \n                onClick={() => document.getElementById('contact').scrollIntoView({ behavior: 'smooth' })}\n                className=\"px-8 py-4 bg-gray-100 hover:bg-gray-200 text-gray-800 font-semibold rounded-lg transition-all duration-300\"\n              >\n                Hubungi Kami\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default WhyUsSection;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,MAAM,EAAEC,WAAW,EAAEC,KAAK,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5F,MAAMC,YAAY,GAAGA,CAAA,KAAM;EACzB,MAAMC,OAAO,GAAG,CACd;IACEC,IAAI,eAAEH,OAAA,CAACP,WAAW;MAACW,SAAS,EAAC;IAAwB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxDC,KAAK,EAAE,oBAAoB;IAC3BC,WAAW,EAAE;EACf,CAAC,EACD;IACEP,IAAI,eAAEH,OAAA,CAACN,QAAQ;MAACU,SAAS,EAAC;IAA0B;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvDC,KAAK,EAAE,kBAAkB;IACzBC,WAAW,EAAE;EACf,CAAC,EACD;IACEP,IAAI,eAAEH,OAAA,CAACL,OAAO;MAACS,SAAS,EAAC;IAAuB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACnDC,KAAK,EAAE,mBAAmB;IAC1BC,WAAW,EAAE;EACf,CAAC,EACD;IACEP,IAAI,eAAEH,OAAA,CAACJ,MAAM;MAACQ,SAAS,EAAC;IAA0B;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACrDC,KAAK,EAAE,uBAAuB;IAC9BC,WAAW,EAAE;EACf,CAAC,EACD;IACEP,IAAI,eAAEH,OAAA,CAACH,WAAW;MAACO,SAAS,EAAC;IAAyB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACzDC,KAAK,EAAE,oBAAoB;IAC3BC,WAAW,EAAE;EACf,CAAC,EACD;IACEP,IAAI,eAAEH,OAAA,CAACF,KAAK;MAACM,SAAS,EAAC;IAAwB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAClDC,KAAK,EAAE,uBAAuB;IAC9BC,WAAW,EAAE;EACf,CAAC,CACF;EAED,oBACEV,OAAA;IAASI,SAAS,EAAC,kBAAkB;IAAAO,QAAA,eACnCX,OAAA;MAAKI,SAAS,EAAC,wBAAwB;MAAAO,QAAA,gBAErCX,OAAA;QAAKI,SAAS,EAAC,mBAAmB;QAAAO,QAAA,gBAChCX,OAAA;UAAII,SAAS,EAAC,mDAAmD;UAAAO,QAAA,EAAC;QAElE;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLR,OAAA;UAAGI,SAAS,EAAC,yCAAyC;UAAAO,QAAA,EAAC;QAEvD;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGNR,OAAA;QAAKI,SAAS,EAAC,wEAAwE;QAAAO,QAAA,EACpFT,OAAO,CAACU,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,kBACzBd,OAAA;UAEEI,SAAS,EAAC,oHAAoH;UAAAO,QAAA,eAE9HX,OAAA;YAAKI,SAAS,EAAC,aAAa;YAAAO,QAAA,gBAC1BX,OAAA;cAAKI,SAAS,EAAC,wIAAwI;cAAAO,QAAA,EACpJE,MAAM,CAACV;YAAI;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACNR,OAAA;cAAII,SAAS,EAAC,sCAAsC;cAAAO,QAAA,EACjDE,MAAM,CAACJ;YAAK;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC,eACLR,OAAA;cAAGI,SAAS,EAAC,+BAA+B;cAAAO,QAAA,EACzCE,MAAM,CAACH;YAAW;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC,GAbDM,KAAK;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAcP,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNR,OAAA;QAAKI,SAAS,EAAC,mBAAmB;QAAAO,QAAA,eAChCX,OAAA;UAAKI,SAAS,EAAC,sDAAsD;UAAAO,QAAA,gBACnEX,OAAA;YAAII,SAAS,EAAC,uCAAuC;YAAAO,QAAA,EAAC;UAEtD;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLR,OAAA;YAAGI,SAAS,EAAC,4BAA4B;YAAAO,QAAA,EAAC;UAE1C;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJR,OAAA;YAAKI,SAAS,EAAC,gDAAgD;YAAAO,QAAA,gBAC7DX,OAAA;cACEe,OAAO,EAAEA,CAAA,KAAMC,QAAQ,CAACC,cAAc,CAAC,UAAU,CAAC,CAACC,cAAc,CAAC;gBAAEC,QAAQ,EAAE;cAAS,CAAC,CAAE;cAC1Ff,SAAS,EAAC,mIAAmI;cAAAO,QAAA,EAC9I;YAED;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTR,OAAA;cACEe,OAAO,EAAEA,CAAA,KAAMC,QAAQ,CAACC,cAAc,CAAC,SAAS,CAAC,CAACC,cAAc,CAAC;gBAAEC,QAAQ,EAAE;cAAS,CAAC,CAAE;cACzFf,SAAS,EAAC,4GAA4G;cAAAO,QAAA,EACvH;YAED;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAACY,EAAA,GAjGInB,YAAY;AAmGlB,eAAeA,YAAY;AAAC,IAAAmB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}