import React from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { FaUsers, FaCog, FaChartBar, FaSignOutAlt, FaUserShield } from 'react-icons/fa';
import authService from '../../services/authService';

const AdminDashboard = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const currentUser = authService.getCurrentUser();

  const navLinks = [
    { path: '/admin/users', label: 'Kelola Pengguna', icon: FaUsers },
    { path: '/admin/settings', label: 'Pengaturan', icon: FaCog },
    { path: '/admin/reports', label: 'Laporan', icon: FaChartBar },
  ];

  const handleLogout = async () => {
    await authService.logout();
    navigate('/login');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex">
      {/* Sidebar */}
      <div className="w-64 bg-gray-800 text-white flex flex-col shadow-lg">
        <div className="p-6 border-b border-gray-700">
          <div className="flex items-center">
            <FaUserShield className="h-8 w-8 text-blue-300 mr-3" />
            <div>
              <h2 className="text-xl font-bold text-blue-300">HaloBantu</h2>
              <p className="text-sm text-gray-300">Admin Panel</p>
            </div>
          </div>
        </div>
        <nav className="flex-1 px-4 py-8 space-y-3">
          {navLinks.map((link) => {
            const IconComponent = link.icon;
            return (
              <Link
                key={link.path}
                to={link.path}
                className={`flex items-center px-5 py-3 rounded-lg text-lg font-medium transition-all duration-300
                  ${location.pathname === link.path
                    ? 'bg-blue-600 text-white shadow-md'
                    : 'hover:bg-gray-700 hover:text-blue-200'
                  }`}
              >
                <IconComponent className="h-5 w-5 mr-3" />
                {link.label}
              </Link>
            );
          })}
        </nav>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        <header className="bg-white shadow-md p-6 border-b border-gray-200 flex justify-between items-center">
          <h1 className="text-3xl font-bold text-gray-800">Admin Dashboard</h1>
          <div className="flex items-center space-x-4">
            <div className="flex items-center text-sm text-gray-700">
              <FaUserShield className="h-4 w-4 mr-2" />
              <span>Halo, {currentUser?.firstName || currentUser?.username}!</span>
              <span className="ml-2 px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full">
                {currentUser?.role}
              </span>
            </div>
            <button
              onClick={handleLogout}
              className="flex items-center px-4 py-2 text-sm text-red-600 bg-red-100 rounded-md hover:bg-red-200 transition-colors font-semibold"
            >
              <FaSignOutAlt className="h-4 w-4 mr-1" />
              Logout
            </button>
          </div>
        </header>
        <main className="flex-1 p-10">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            {/* Stats Cards */}
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="p-3 rounded-full bg-blue-100">
                  <FaUsers className="h-6 w-6 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Users</p>
                  <p className="text-2xl font-semibold text-gray-900">2</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="p-3 rounded-full bg-green-100">
                  <FaChartBar className="h-6 w-6 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Active Sessions</p>
                  <p className="text-2xl font-semibold text-gray-900">1</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="p-3 rounded-full bg-purple-100">
                  <FaCog className="h-6 w-6 text-purple-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">System Status</p>
                  <p className="text-2xl font-semibold text-gray-900">Online</p>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-xl p-8 border border-gray-100">
            <h2 className="text-3xl font-extrabold text-gray-800 mb-4">Selamat Datang di Admin Panel</h2>
            <p className="text-gray-600 text-lg leading-relaxed mb-6">
              Gunakan menu navigasi di samping untuk mengelola berbagai aspek aplikasi HaloBantu.
              Sebagai admin, Anda memiliki akses penuh untuk mengelola pengguna dan sistem.
            </p>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="border border-gray-200 rounded-lg p-4">
                <h3 className="font-semibold text-gray-900 mb-2">Kelola Pengguna</h3>
                <p className="text-sm text-gray-600">Tambah, edit, atau hapus akun pengguna dan admin</p>
              </div>
              <div className="border border-gray-200 rounded-lg p-4">
                <h3 className="font-semibold text-gray-900 mb-2">Pengaturan Sistem</h3>
                <p className="text-sm text-gray-600">Konfigurasi pengaturan aplikasi dan keamanan</p>
              </div>
              <div className="border border-gray-200 rounded-lg p-4">
                <h3 className="font-semibold text-gray-900 mb-2">Laporan & Analytics</h3>
                <p className="text-sm text-gray-600">Lihat statistik penggunaan dan laporan sistem</p>
              </div>
              <div className="border border-gray-200 rounded-lg p-4">
                <h3 className="font-semibold text-gray-900 mb-2">Monitoring</h3>
                <p className="text-sm text-gray-600">Monitor aktivitas sistem dan keamanan</p>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
};

export default AdminDashboard; 