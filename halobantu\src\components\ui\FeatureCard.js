import React from 'react';

const FeatureCard = ({ title, description, icon, color, onClick }) => {
  return (
    <div 
      onClick={onClick}
      className={`
        relative overflow-hidden rounded-2xl p-6 cursor-pointer
        transform transition-all duration-300 hover:scale-105
        ${color} text-white
      `}
    >
      <div className="relative z-10">
        <div className="text-4xl mb-4">{icon}</div>
        <h3 className="text-2xl font-bold mb-2">{title}</h3>
        <p className="opacity-90">{description}</p>
      </div>
      <div className="absolute -right-10 -bottom-10 w-40 h-40 bg-white opacity-10 rounded-full"></div>
    </div>
  );
};

export default FeatureCard; 