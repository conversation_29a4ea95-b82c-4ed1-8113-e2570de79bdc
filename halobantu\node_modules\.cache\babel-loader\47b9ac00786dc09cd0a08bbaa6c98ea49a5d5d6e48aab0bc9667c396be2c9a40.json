{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\JOKI MAKALAH\\\\halobantu1\\\\halobantu\\\\src\\\\features\\\\dashboard\\\\Dashboard.js\";\nimport React from 'react';\nimport HeroSection from './components/HeroSection';\nimport ServicesSection from './components/ServicesSection';\nimport AboutSection from './components/AboutSection';\nimport WhyUsSection from './components/WhyUsSection';\nimport GuaranteesSection from './components/GuaranteesSection';\nimport ConsultationSection from './components/ConsultationSection';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Dashboard = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white\",\n    children: /*#__PURE__*/_jsxDEV(\"main\", {\n      children: [/*#__PURE__*/_jsxDEV(HeroSection, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 14,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ServicesSection, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 15,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AboutSection, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(WhyUsSection, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(GuaranteesSection, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ConsultationSection, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 11,\n    columnNumber: 5\n  }, this);\n};\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "HeroSection", "ServicesSection", "AboutSection", "WhyUsSection", "GuaranteesSection", "ConsultationSection", "jsxDEV", "_jsxDEV", "Dashboard", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/JOKI MAKALAH/halobantu1/halobantu/src/features/dashboard/Dashboard.js"], "sourcesContent": ["import React from 'react';\r\nimport HeroSection from './components/HeroSection';\r\nimport ServicesSection from './components/ServicesSection';\r\nimport AboutSection from './components/AboutSection';\r\nimport WhyUsSection from './components/WhyUsSection';\r\nimport GuaranteesSection from './components/GuaranteesSection';\r\nimport ConsultationSection from './components/ConsultationSection';\r\n\r\nconst Dashboard = () => {\r\n  return (\r\n    <div className=\"bg-white\">\r\n      {/* Main Content */}\r\n      <main>\r\n        <HeroSection />\r\n        <ServicesSection />\r\n        <AboutSection />\r\n        <WhyUsSection />\r\n        <GuaranteesSection />\r\n        <ConsultationSection />\r\n      </main>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Dashboard;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,eAAe,MAAM,8BAA8B;AAC1D,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAOC,iBAAiB,MAAM,gCAAgC;AAC9D,OAAOC,mBAAmB,MAAM,kCAAkC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnE,MAAMC,SAAS,GAAGA,CAAA,KAAM;EACtB,oBACED,OAAA;IAAKE,SAAS,EAAC,UAAU;IAAAC,QAAA,eAEvBH,OAAA;MAAAG,QAAA,gBACEH,OAAA,CAACP,WAAW;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACfP,OAAA,CAACN,eAAe;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACnBP,OAAA,CAACL,YAAY;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAChBP,OAAA,CAACJ,YAAY;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAChBP,OAAA,CAACH,iBAAiB;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACrBP,OAAA,CAACF,mBAAmB;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACC,EAAA,GAdIP,SAAS;AAgBf,eAAeA,SAAS;AAAC,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}