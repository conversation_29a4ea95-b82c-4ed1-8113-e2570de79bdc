{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\JOKI MAKALAH\\\\halobantu1\\\\halobantu\\\\src\\\\features\\\\dashboard\\\\components\\\\HeroSection.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HeroSection = () => {\n  _s();\n  const [heroImage, setHeroImage] = useState('');\n  useEffect(() => {\n    // Using a high-quality automotive image from Pexels\n    // This is a direct link to a professional car service image\n    setHeroImage('https://images.pexels.com/photos/3806288/pexels-photo-3806288.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&fit=crop');\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    id: \"home\",\n    className: \"relative h-screen flex items-center justify-center overflow-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 bg-cover bg-center bg-no-repeat\",\n      style: {\n        backgroundImage: `url(${heroImage})`\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 bg-black/50\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative z-10 text-center text-white px-4 max-w-4xl mx-auto\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-5xl md:text-7xl font-bold mb-6 leading-tight\",\n        children: \"Future X\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-xl md:text-2xl mb-8 opacity-90 leading-relaxed\",\n        children: \"Solusi masa depan untuk semua kebutuhan otomotif dan teknologi Anda. Kami menghadirkan inovasi terdepan dengan layanan berkualitas tinggi dan teknologi canggih.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => document.getElementById('services').scrollIntoView({\n            behavior: 'smooth'\n          }),\n          className: \"px-8 py-4 bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-lg transition-all duration-300 transform hover:scale-105\",\n          children: \"Lihat Layanan\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => document.getElementById('contact').scrollIntoView({\n            behavior: 'smooth'\n          }),\n          className: \"px-8 py-4 bg-transparent border-2 border-white text-white hover:bg-white hover:text-gray-800 font-semibold rounded-lg transition-all duration-300\",\n          children: \"Hubungi Kami\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white animate-bounce\",\n      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n        className: \"w-6 h-6\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        viewBox: \"0 0 24 24\",\n        children: /*#__PURE__*/_jsxDEV(\"path\", {\n          strokeLinecap: \"round\",\n          strokeLinejoin: \"round\",\n          strokeWidth: 2,\n          d: \"M19 14l-7 7m0 0l-7-7m7 7V3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 13,\n    columnNumber: 5\n  }, this);\n};\n_s(HeroSection, \"MQgnoYCw0ntAxeeZu7S829aBA38=\");\n_c = HeroSection;\nexport default HeroSection;\nvar _c;\n$RefreshReg$(_c, \"HeroSection\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "HeroSection", "_s", "heroImage", "setHeroImage", "id", "className", "children", "style", "backgroundImage", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "document", "getElementById", "scrollIntoView", "behavior", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/JOKI MAKALAH/halobantu1/halobantu/src/features/dashboard/components/HeroSection.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\n\nconst HeroSection = () => {\n  const [heroImage, setHeroImage] = useState('');\n\n  useEffect(() => {\n    // Using a high-quality automotive image from Pexels\n    // This is a direct link to a professional car service image\n    setHeroImage('https://images.pexels.com/photos/3806288/pexels-photo-3806288.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&fit=crop');\n  }, []);\n\n  return (\n    <section id=\"home\" className=\"relative h-screen flex items-center justify-center overflow-hidden\">\n      {/* Background Image */}\n      <div \n        className=\"absolute inset-0 bg-cover bg-center bg-no-repeat\"\n        style={{\n          backgroundImage: `url(${heroImage})`,\n        }}\n      >\n        {/* Dark overlay */}\n        <div className=\"absolute inset-0 bg-black/50\"></div>\n      </div>\n\n      {/* Content */}\n      <div className=\"relative z-10 text-center text-white px-4 max-w-4xl mx-auto\">\n        <h1 className=\"text-5xl md:text-7xl font-bold mb-6 leading-tight\">\n          Future X\n        </h1>\n        <p className=\"text-xl md:text-2xl mb-8 opacity-90 leading-relaxed\">\n          Solusi masa depan untuk semua kebutuhan otomotif dan teknologi Anda.\n          Kami menghadirkan inovasi terdepan dengan layanan berkualitas tinggi dan teknologi canggih.\n        </p>\n        <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n          <button \n            onClick={() => document.getElementById('services').scrollIntoView({ behavior: 'smooth' })}\n            className=\"px-8 py-4 bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-lg transition-all duration-300 transform hover:scale-105\"\n          >\n            Lihat Layanan\n          </button>\n          <button \n            onClick={() => document.getElementById('contact').scrollIntoView({ behavior: 'smooth' })}\n            className=\"px-8 py-4 bg-transparent border-2 border-white text-white hover:bg-white hover:text-gray-800 font-semibold rounded-lg transition-all duration-300\"\n          >\n            Hubungi Kami\n          </button>\n        </div>\n      </div>\n\n      {/* Scroll indicator */}\n      <div className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white animate-bounce\">\n        <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 14l-7 7m0 0l-7-7m7 7V3\" />\n        </svg>\n      </div>\n    </section>\n  );\n};\n\nexport default HeroSection;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGP,QAAQ,CAAC,EAAE,CAAC;EAE9CC,SAAS,CAAC,MAAM;IACd;IACA;IACAM,YAAY,CAAC,qHAAqH,CAAC;EACrI,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEJ,OAAA;IAASK,EAAE,EAAC,MAAM;IAACC,SAAS,EAAC,oEAAoE;IAAAC,QAAA,gBAE/FP,OAAA;MACEM,SAAS,EAAC,kDAAkD;MAC5DE,KAAK,EAAE;QACLC,eAAe,EAAE,OAAON,SAAS;MACnC,CAAE;MAAAI,QAAA,eAGFP,OAAA;QAAKM,SAAS,EAAC;MAA8B;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjD,CAAC,eAGNb,OAAA;MAAKM,SAAS,EAAC,6DAA6D;MAAAC,QAAA,gBAC1EP,OAAA;QAAIM,SAAS,EAAC,mDAAmD;QAAAC,QAAA,EAAC;MAElE;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLb,OAAA;QAAGM,SAAS,EAAC,qDAAqD;QAAAC,QAAA,EAAC;MAGnE;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJb,OAAA;QAAKM,SAAS,EAAC,gDAAgD;QAAAC,QAAA,gBAC7DP,OAAA;UACEc,OAAO,EAAEA,CAAA,KAAMC,QAAQ,CAACC,cAAc,CAAC,UAAU,CAAC,CAACC,cAAc,CAAC;YAAEC,QAAQ,EAAE;UAAS,CAAC,CAAE;UAC1FZ,SAAS,EAAC,mIAAmI;UAAAC,QAAA,EAC9I;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTb,OAAA;UACEc,OAAO,EAAEA,CAAA,KAAMC,QAAQ,CAACC,cAAc,CAAC,SAAS,CAAC,CAACC,cAAc,CAAC;YAAEC,QAAQ,EAAE;UAAS,CAAC,CAAE;UACzFZ,SAAS,EAAC,mJAAmJ;UAAAC,QAAA,EAC9J;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNb,OAAA;MAAKM,SAAS,EAAC,iFAAiF;MAAAC,QAAA,eAC9FP,OAAA;QAAKM,SAAS,EAAC,SAAS;QAACa,IAAI,EAAC,MAAM;QAACC,MAAM,EAAC,cAAc;QAACC,OAAO,EAAC,WAAW;QAAAd,QAAA,eAC5EP,OAAA;UAAMsB,aAAa,EAAC,OAAO;UAACC,cAAc,EAAC,OAAO;UAACC,WAAW,EAAE,CAAE;UAACC,CAAC,EAAC;QAA4B;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAACX,EAAA,CAvDID,WAAW;AAAAyB,EAAA,GAAXzB,WAAW;AAyDjB,eAAeA,WAAW;AAAC,IAAAyB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}