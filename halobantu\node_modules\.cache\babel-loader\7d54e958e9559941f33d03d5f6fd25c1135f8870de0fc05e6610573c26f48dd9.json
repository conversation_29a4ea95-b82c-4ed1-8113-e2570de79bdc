{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\JOKI MAKALAH\\\\halobantu1\\\\halobantu\\\\src\\\\components\\\\ui\\\\BackButton.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { FaArrowLeft } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst BackButton = ({\n  targetPath = '/dashboard',\n  label = 'Kembali ke Dashboard'\n}) => {\n  _s();\n  const navigate = useNavigate();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fixed top-4 left-4 z-50 bg-red-500 p-3 rounded-lg shadow-xl border-4 border-red-800 animate-pulse\",\n    children: /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: () => navigate(targetPath),\n      className: \"flex items-center text-white text-lg font-bold hover:text-gray-200\",\n      children: [/*#__PURE__*/_jsxDEV(FaArrowLeft, {\n        className: \"mr-2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 14,\n        columnNumber: 9\n      }, this), label, \" (CEK INI)\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 9,\n    columnNumber: 5\n  }, this);\n};\n_s(BackButton, \"CzcTeTziyjMsSrAVmHuCCb6+Bfg=\", false, function () {\n  return [useNavigate];\n});\n_c = BackButton;\nexport default BackButton;\nvar _c;\n$RefreshReg$(_c, \"BackButton\");", "map": {"version": 3, "names": ["React", "useNavigate", "FaArrowLeft", "jsxDEV", "_jsxDEV", "BackButton", "targetPath", "label", "_s", "navigate", "className", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/JOKI MAKALAH/halobantu1/halobantu/src/components/ui/BackButton.js"], "sourcesContent": ["import React from 'react';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport { FaArrowLeft } from 'react-icons/fa';\r\n\r\nconst BackButton = ({ targetPath = '/dashboard', label = 'Kembali ke Dashboard' }) => {\r\n  const navigate = useNavigate();\r\n\r\n  return (\r\n    <div className=\"fixed top-4 left-4 z-50 bg-red-500 p-3 rounded-lg shadow-xl border-4 border-red-800 animate-pulse\">\r\n      <button\r\n        onClick={() => navigate(targetPath)}\r\n        className=\"flex items-center text-white text-lg font-bold hover:text-gray-200\"\r\n      >\r\n        <FaArrowLeft className=\"mr-2\" />\r\n        {label} (CEK INI)\r\n      </button>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default BackButton; "], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7C,MAAMC,UAAU,GAAGA,CAAC;EAAEC,UAAU,GAAG,YAAY;EAAEC,KAAK,GAAG;AAAuB,CAAC,KAAK;EAAAC,EAAA;EACpF,MAAMC,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAE9B,oBACEG,OAAA;IAAKM,SAAS,EAAC,mGAAmG;IAAAC,QAAA,eAChHP,OAAA;MACEQ,OAAO,EAAEA,CAAA,KAAMH,QAAQ,CAACH,UAAU,CAAE;MACpCI,SAAS,EAAC,oEAAoE;MAAAC,QAAA,gBAE9EP,OAAA,CAACF,WAAW;QAACQ,SAAS,EAAC;MAAM;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAC/BT,KAAK,EAAC,YACT;IAAA;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACR,EAAA,CAdIH,UAAU;EAAA,QACGJ,WAAW;AAAA;AAAAgB,EAAA,GADxBZ,UAAU;AAgBhB,eAAeA,UAAU;AAAC,IAAAY,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}