{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\JOKI MAKALAH\\\\halobantu1\\\\halobantu\\\\src\\\\App.js\";\nimport React from 'react';\nimport { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom';\nimport Layout from './components/layout/Layout';\nimport ProtectedRoute from './components/common/ProtectedRoute';\nimport Login from './features/auth/Login';\nimport Register from './features/auth/Register';\nimport Dashboard from './features/dashboard/Dashboard';\nimport PsychologyPage from './features/psychology/pages/PsychologyPage';\nimport ConsultationDetail from './features/psychology/pages/ConsultationDetail';\nimport DoctorBiography from './features/psychology/pages/DoctorBiography';\nimport WorkshopPage from './features/workshop/WorkshopPage';\nimport DailyServicesPage from './features/daily-services/DailyServicesPage';\nimport AdminDashboard from './features/admin/AdminDashboard';\nimport './styles/App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(BrowserRouter, {\n    children: /*#__PURE__*/_jsxDEV(Routes, {\n      children: [/*#__PURE__*/_jsxDEV(Route, {\n        path: \"/\",\n        element: /*#__PURE__*/_jsxDEV(Navigate, {\n          to: \"/login\",\n          replace: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 34\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 21,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/login\",\n        element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 39\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/register\",\n        element: /*#__PURE__*/_jsxDEV(Register, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 42\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/dashboard\",\n        element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n          requiredRole: \"user\",\n          children: /*#__PURE__*/_jsxDEV(Layout, {\n            children: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 32,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 32,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/admin\",\n        element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n          requiredRole: \"admin\",\n          children: /*#__PURE__*/_jsxDEV(AdminDashboard, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/workshop\",\n        element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n          children: /*#__PURE__*/_jsxDEV(Layout, {\n            children: /*#__PURE__*/_jsxDEV(WorkshopPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 52,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/psychology\",\n        element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n          children: /*#__PURE__*/_jsxDEV(Layout, {\n            children: /*#__PURE__*/_jsxDEV(PsychologyPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 60,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/psychology/consultation\",\n        element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n          children: /*#__PURE__*/_jsxDEV(Layout, {\n            children: /*#__PURE__*/_jsxDEV(ConsultationDetail, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 68,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/psychology/doctor/:id\",\n        element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n          children: /*#__PURE__*/_jsxDEV(DoctorBiography, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/services\",\n        element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n          children: /*#__PURE__*/_jsxDEV(Layout, {\n            children: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 84,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/daily-services\",\n        element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n          children: /*#__PURE__*/_jsxDEV(Layout, {\n            children: /*#__PURE__*/_jsxDEV(DailyServicesPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 92,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 18,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Routes", "Route", "Navigate", "Layout", "ProtectedRoute", "<PERSON><PERSON>", "Register", "Dashboard", "PsychologyPage", "ConsultationDetail", "DoctorBiography", "WorkshopPage", "DailyServicesPage", "AdminDashboard", "jsxDEV", "_jsxDEV", "App", "children", "path", "element", "to", "replace", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "requiredRole", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/JOKI MAKALAH/halobantu1/halobantu/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom';\nimport Layout from './components/layout/Layout';\nimport ProtectedRoute from './components/common/ProtectedRoute';\nimport Login from './features/auth/Login';\nimport Register from './features/auth/Register';\nimport Dashboard from './features/dashboard/Dashboard';\nimport PsychologyPage from './features/psychology/pages/PsychologyPage';\nimport ConsultationDetail from './features/psychology/pages/ConsultationDetail';\nimport DoctorBiography from './features/psychology/pages/DoctorBiography';\nimport WorkshopPage from './features/workshop/WorkshopPage';\nimport DailyServicesPage from './features/daily-services/DailyServicesPage';\nimport AdminDashboard from './features/admin/AdminDashboard';\nimport './styles/App.css';\n\nfunction App() {\n  return (\n    <BrowserRouter>\n      <Routes>\n        {/* Redirect root to login */}\n        <Route path=\"/\" element={<Navigate to=\"/login\" replace />} />\n\n        {/* Auth Routes - No Layout */}\n        <Route path=\"/login\" element={<Login />} />\n        <Route path=\"/register\" element={<Register />} />\n\n        {/* Protected Routes - User Dashboard */}\n        <Route\n          path=\"/dashboard\"\n          element={\n            <ProtectedRoute requiredRole=\"user\">\n              <Layout><Dashboard /></Layout>\n            </ProtectedRoute>\n          }\n        />\n\n        {/* Protected Routes - Admin Dashboard */}\n        <Route\n          path=\"/admin\"\n          element={\n            <ProtectedRoute requiredRole=\"admin\">\n              <AdminDashboard />\n            </ProtectedRoute>\n          }\n        />\n\n        {/* Other Protected Routes - With Layout (for future features) */}\n        <Route\n          path=\"/workshop\"\n          element={\n            <ProtectedRoute>\n              <Layout><WorkshopPage /></Layout>\n            </ProtectedRoute>\n          }\n        />\n        <Route\n          path=\"/psychology\"\n          element={\n            <ProtectedRoute>\n              <Layout><PsychologyPage /></Layout>\n            </ProtectedRoute>\n          }\n        />\n        <Route\n          path=\"/psychology/consultation\"\n          element={\n            <ProtectedRoute>\n              <Layout><ConsultationDetail /></Layout>\n            </ProtectedRoute>\n          }\n        />\n        <Route\n          path=\"/psychology/doctor/:id\"\n          element={\n            <ProtectedRoute>\n              <DoctorBiography />\n            </ProtectedRoute>\n          }\n        />\n        <Route\n          path=\"/services\"\n          element={\n            <ProtectedRoute>\n              <Layout><Dashboard /></Layout>\n            </ProtectedRoute>\n          }\n        />\n        <Route\n          path=\"/daily-services\"\n          element={\n            <ProtectedRoute>\n              <Layout><DailyServicesPage /></Layout>\n            </ProtectedRoute>\n          }\n        />\n      </Routes>\n    </BrowserRouter>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AACzE,OAAOC,MAAM,MAAM,4BAA4B;AAC/C,OAAOC,cAAc,MAAM,oCAAoC;AAC/D,OAAOC,KAAK,MAAM,uBAAuB;AACzC,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,OAAOC,SAAS,MAAM,gCAAgC;AACtD,OAAOC,cAAc,MAAM,4CAA4C;AACvE,OAAOC,kBAAkB,MAAM,gDAAgD;AAC/E,OAAOC,eAAe,MAAM,6CAA6C;AACzE,OAAOC,YAAY,MAAM,kCAAkC;AAC3D,OAAOC,iBAAiB,MAAM,6CAA6C;AAC3E,OAAOC,cAAc,MAAM,iCAAiC;AAC5D,OAAO,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAAChB,aAAa;IAAAkB,QAAA,eACZF,OAAA,CAACf,MAAM;MAAAiB,QAAA,gBAELF,OAAA,CAACd,KAAK;QAACiB,IAAI,EAAC,GAAG;QAACC,OAAO,eAAEJ,OAAA,CAACb,QAAQ;UAACkB,EAAE,EAAC,QAAQ;UAACC,OAAO;QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAG7DV,OAAA,CAACd,KAAK;QAACiB,IAAI,EAAC,QAAQ;QAACC,OAAO,eAAEJ,OAAA,CAACV,KAAK;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3CV,OAAA,CAACd,KAAK;QAACiB,IAAI,EAAC,WAAW;QAACC,OAAO,eAAEJ,OAAA,CAACT,QAAQ;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAGjDV,OAAA,CAACd,KAAK;QACJiB,IAAI,EAAC,YAAY;QACjBC,OAAO,eACLJ,OAAA,CAACX,cAAc;UAACsB,YAAY,EAAC,MAAM;UAAAT,QAAA,eACjCF,OAAA,CAACZ,MAAM;YAAAc,QAAA,eAACF,OAAA,CAACR,SAAS;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MACjB;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGFV,OAAA,CAACd,KAAK;QACJiB,IAAI,EAAC,QAAQ;QACbC,OAAO,eACLJ,OAAA,CAACX,cAAc;UAACsB,YAAY,EAAC,OAAO;UAAAT,QAAA,eAClCF,OAAA,CAACF,cAAc;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MACjB;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGFV,OAAA,CAACd,KAAK;QACJiB,IAAI,EAAC,WAAW;QAChBC,OAAO,eACLJ,OAAA,CAACX,cAAc;UAAAa,QAAA,eACbF,OAAA,CAACZ,MAAM;YAAAc,QAAA,eAACF,OAAA,CAACJ,YAAY;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB;MACjB;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACFV,OAAA,CAACd,KAAK;QACJiB,IAAI,EAAC,aAAa;QAClBC,OAAO,eACLJ,OAAA,CAACX,cAAc;UAAAa,QAAA,eACbF,OAAA,CAACZ,MAAM;YAAAc,QAAA,eAACF,OAAA,CAACP,cAAc;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB;MACjB;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACFV,OAAA,CAACd,KAAK;QACJiB,IAAI,EAAC,0BAA0B;QAC/BC,OAAO,eACLJ,OAAA,CAACX,cAAc;UAAAa,QAAA,eACbF,OAAA,CAACZ,MAAM;YAAAc,QAAA,eAACF,OAAA,CAACN,kBAAkB;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB;MACjB;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACFV,OAAA,CAACd,KAAK;QACJiB,IAAI,EAAC,wBAAwB;QAC7BC,OAAO,eACLJ,OAAA,CAACX,cAAc;UAAAa,QAAA,eACbF,OAAA,CAACL,eAAe;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MACjB;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACFV,OAAA,CAACd,KAAK;QACJiB,IAAI,EAAC,WAAW;QAChBC,OAAO,eACLJ,OAAA,CAACX,cAAc;UAAAa,QAAA,eACbF,OAAA,CAACZ,MAAM;YAAAc,QAAA,eAACF,OAAA,CAACR,SAAS;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MACjB;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACFV,OAAA,CAACd,KAAK;QACJiB,IAAI,EAAC,iBAAiB;QACtBC,OAAO,eACLJ,OAAA,CAACX,cAAc;UAAAa,QAAA,eACbF,OAAA,CAACZ,MAAM;YAAAc,QAAA,eAACF,OAAA,CAACH,iBAAiB;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB;MACjB;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEpB;AAACE,EAAA,GAnFQX,GAAG;AAqFZ,eAAeA,GAAG;AAAC,IAAAW,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}