const bcrypt = require('bcryptjs');

async function generatePassword() {
  const password = 'Password123';
  const saltRounds = 12;
  
  try {
    const hashedPassword = await bcrypt.hash(password, saltRounds);
    console.log('🔑 Original Password:', password);
    console.log('🔒 Hashed Password:', hashedPassword);
    
    // Test verification
    const isValid = await bcrypt.compare(password, hashedPassword);
    console.log('✅ Verification Test:', isValid ? 'PASSED' : 'FAILED');
    
    console.log('\n📋 SQL Query to update database:');
    console.log(`UPDATE users SET password = '${hashedPassword}' WHERE email = '<EMAIL>';`);
    console.log(`UPDATE users SET password = '${hashedPassword}' WHERE email = '<EMAIL>';`);
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

generatePassword();
