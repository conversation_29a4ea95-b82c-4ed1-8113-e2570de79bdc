-- Database Schema untuk HaloBantu
-- Jalankan script ini di phpMyAdmin setelah membuat database halobantu_db

USE halobantu_db;

-- Tabel Users
CREATE TABLE IF NOT EXISTS `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `email` varchar(255) NOT NULL,
  `password` varchar(255) NOT NULL,
  `firstName` varchar(100) NOT NULL,
  `lastName` varchar(100) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `role` enum('user','admin','psychologist') NOT NULL DEFAULT 'user',
  `isActive` tinyint(1) NOT NULL DEFAULT 1,
  `emailVerified` tinyint(1) NOT NULL DEFAULT 0,
  `emailVerificationToken` varchar(255) DEFAULT NULL,
  `passwordResetToken` varchar(255) DEFAULT NULL,
  `passwordResetExpires` datetime DEFAULT NULL,
  `loginAttempts` int(11) NOT NULL DEFAULT 0,
  `lockUntil` datetime DEFAULT NULL,
  `lastLogin` datetime DEFAULT NULL,
  `profilePicture` varchar(500) DEFAULT NULL,
  `createdAt` timestamp NOT NULL DEFAULT current_timestamp(),
  `updatedAt` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `email` (`email`),
  KEY `role` (`role`),
  KEY `isActive` (`isActive`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Insert Admin Default
INSERT INTO `users` (`email`, `password`, `firstName`, `lastName`, `role`, `isActive`, `emailVerified`) 
VALUES (
  '<EMAIL>', 
  '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9qm', -- Password: Admin123!
  'Admin', 
  'HaloBantu', 
  'admin', 
  1, 
  1
) ON DUPLICATE KEY UPDATE email=email;

-- Tabel untuk Psychology Consultations (untuk pengembangan selanjutnya)
CREATE TABLE IF NOT EXISTS `consultations` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `userId` int(11) NOT NULL,
  `psychologistId` int(11) DEFAULT NULL,
  `title` varchar(255) NOT NULL,
  `description` text,
  `status` enum('pending','scheduled','completed','cancelled') NOT NULL DEFAULT 'pending',
  `scheduledAt` datetime DEFAULT NULL,
  `completedAt` datetime DEFAULT NULL,
  `notes` text,
  `createdAt` timestamp NOT NULL DEFAULT current_timestamp(),
  `updatedAt` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `userId` (`userId`),
  KEY `psychologistId` (`psychologistId`),
  KEY `status` (`status`),
  FOREIGN KEY (`userId`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`psychologistId`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Tabel untuk Workshops (untuk pengembangan selanjutnya)
CREATE TABLE IF NOT EXISTS `workshops` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL,
  `description` text,
  `instructorId` int(11) DEFAULT NULL,
  `maxParticipants` int(11) DEFAULT NULL,
  `currentParticipants` int(11) NOT NULL DEFAULT 0,
  `scheduledAt` datetime NOT NULL,
  `duration` int(11) NOT NULL COMMENT 'Duration in minutes',
  `location` varchar(255) DEFAULT NULL,
  `isOnline` tinyint(1) NOT NULL DEFAULT 0,
  `meetingLink` varchar(500) DEFAULT NULL,
  `status` enum('draft','published','ongoing','completed','cancelled') NOT NULL DEFAULT 'draft',
  `createdAt` timestamp NOT NULL DEFAULT current_timestamp(),
  `updatedAt` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `instructorId` (`instructorId`),
  KEY `status` (`status`),
  KEY `scheduledAt` (`scheduledAt`),
  FOREIGN KEY (`instructorId`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Tabel untuk Workshop Registrations (untuk pengembangan selanjutnya)
CREATE TABLE IF NOT EXISTS `workshop_registrations` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `workshopId` int(11) NOT NULL,
  `userId` int(11) NOT NULL,
  `status` enum('registered','attended','cancelled') NOT NULL DEFAULT 'registered',
  `registeredAt` timestamp NOT NULL DEFAULT current_timestamp(),
  `attendedAt` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `workshop_user` (`workshopId`, `userId`),
  KEY `userId` (`userId`),
  KEY `status` (`status`),
  FOREIGN KEY (`workshopId`) REFERENCES `workshops` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`userId`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Tabel untuk Daily Services (untuk pengembangan selanjutnya)
CREATE TABLE IF NOT EXISTS `daily_services` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL,
  `description` text,
  `serviceType` enum('consultation','workshop','therapy','assessment') NOT NULL,
  `providerId` int(11) DEFAULT NULL,
  `isActive` tinyint(1) NOT NULL DEFAULT 1,
  `availableFrom` time DEFAULT NULL,
  `availableTo` time DEFAULT NULL,
  `daysOfWeek` varchar(20) DEFAULT NULL COMMENT 'JSON array of days: ["monday","tuesday",...]',
  `price` decimal(10,2) DEFAULT NULL,
  `duration` int(11) DEFAULT NULL COMMENT 'Duration in minutes',
  `createdAt` timestamp NOT NULL DEFAULT current_timestamp(),
  `updatedAt` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `providerId` (`providerId`),
  KEY `serviceType` (`serviceType`),
  KEY `isActive` (`isActive`),
  FOREIGN KEY (`providerId`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Insert Sample Data untuk Testing
INSERT INTO `users` (`email`, `password`, `firstName`, `lastName`, `role`, `isActive`, `emailVerified`) VALUES
('<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9qm', 'Dr. Sarah', 'Johnson', 'psychologist', 1, 1),
('<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9qm', 'John', 'Doe', 'user', 1, 1)
ON DUPLICATE KEY UPDATE email=email;

-- Tampilkan hasil
SELECT 'Database schema created successfully!' as Status;
SELECT COUNT(*) as TotalUsers FROM users;
SELECT email, role, isActive FROM users;
