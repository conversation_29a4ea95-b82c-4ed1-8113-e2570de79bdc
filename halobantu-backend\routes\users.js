const express = require('express');
const router = express.Router();

// Import controllers
const {
  getAllUsers,
  getUserById,
  updateProfile,
  changePassword,
  deactivateAccount,
  updateUserRole,
  toggleUserStatus
} = require('../controllers/userController');

// Import middleware
const {
  verifyToken,
  requireAdmin,
  requireOwnershipOrAdmin
} = require('../middleware/auth');

const {
  validateProfileUpdate,
  validatePasswordChange
} = require('../middleware/validation');

// Protected routes - require authentication
router.use(verifyToken);

// User profile routes
router.get('/profile', updateProfile);
router.put('/profile', validateProfileUpdate, updateProfile);
router.put('/change-password', validatePasswordChange, changePassword);
router.delete('/deactivate', deactivateAccount);

// Admin routes - require admin role
router.get('/', requireAdmin, getAllUsers);
router.get('/:id', requireOwnershipOrAdmin((req) => req.params.id), getUserById);
router.put('/:id/role', requireAdmin, updateUserRole);
router.put('/:id/toggle-status', requireAdmin, toggleUserStatus);

module.exports = router;
