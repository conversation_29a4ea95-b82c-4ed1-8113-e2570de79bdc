[{"C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\index.js": "1", "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\App.js": "2", "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\components\\common\\ProtectedRoute.js": "4", "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\components\\layout\\Layout.js": "5", "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\features\\dashboard\\Dashboard.js": "6", "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\features\\auth\\Login.js": "7", "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\features\\auth\\Register.js": "8", "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\features\\admin\\AdminDashboard.js": "9", "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\features\\workshop\\WorkshopPage.js": "10", "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\features\\daily-services\\DailyServicesPage.js": "11", "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\features\\psychology\\pages\\DoctorBiography.js": "12", "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\features\\psychology\\pages\\ConsultationDetail.js": "13", "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\features\\psychology\\pages\\PsychologyPage.js": "14", "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\components\\layout\\Header.js": "15", "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\components\\layout\\Footer.js": "16", "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\services\\authService.js": "17", "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\components\\ui\\BackButton.js": "18", "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\services\\api.js": "19", "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\features\\dashboard\\components\\ConsultationSection.js": "20", "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\features\\dashboard\\components\\HeroSection.js": "21", "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\features\\dashboard\\components\\ServicesSection.js": "22", "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\features\\dashboard\\components\\AboutSection.js": "23", "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\features\\dashboard\\components\\GuaranteesSection.js": "24", "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\features\\dashboard\\components\\WhyUsSection.js": "25"}, {"size": 542, "mtime": 1749559665369, "results": "26", "hashOfConfig": "27"}, {"size": 2977, "mtime": 1749636619475, "results": "28", "hashOfConfig": "27"}, {"size": 362, "mtime": 1749483836823, "results": "29", "hashOfConfig": "27"}, {"size": 805, "mtime": 1749636511763, "results": "30", "hashOfConfig": "27"}, {"size": 327, "mtime": 1749558261407, "results": "31", "hashOfConfig": "27"}, {"size": 735, "mtime": 1749636597475, "results": "32", "hashOfConfig": "27"}, {"size": 8714, "mtime": 1749636479499, "results": "33", "hashOfConfig": "27"}, {"size": 11226, "mtime": 1749631759537, "results": "34", "hashOfConfig": "27"}, {"size": 6741, "mtime": 1749632274617, "results": "35", "hashOfConfig": "27"}, {"size": 16432, "mtime": 1749563193810, "results": "36", "hashOfConfig": "27"}, {"size": 7073, "mtime": 1749558443270, "results": "37", "hashOfConfig": "27"}, {"size": 7484, "mtime": 1749563767075, "results": "38", "hashOfConfig": "27"}, {"size": 5020, "mtime": 1749558483627, "results": "39", "hashOfConfig": "27"}, {"size": 12433, "mtime": 1749563625653, "results": "40", "hashOfConfig": "27"}, {"size": 4614, "mtime": 1749558219576, "results": "41", "hashOfConfig": "27"}, {"size": 5335, "mtime": 1749558250334, "results": "42", "hashOfConfig": "27"}, {"size": 2421, "mtime": 1749636495587, "results": "43", "hashOfConfig": "27"}, {"size": 680, "mtime": 1749500724181, "results": "44", "hashOfConfig": "27"}, {"size": 1032, "mtime": 1749631537985, "results": "45", "hashOfConfig": "27"}, {"size": 1550, "mtime": 1749556926195, "results": "46", "hashOfConfig": "27"}, {"size": 2518, "mtime": 1749555567080, "results": "47", "hashOfConfig": "27"}, {"size": 3815, "mtime": 1749555784996, "results": "48", "hashOfConfig": "27"}, {"size": 4333, "mtime": 1749555603273, "results": "49", "hashOfConfig": "27"}, {"size": 5457, "mtime": 1749555943277, "results": "50", "hashOfConfig": "27"}, {"size": 4328, "mtime": 1749555882406, "results": "51", "hashOfConfig": "27"}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1afa335", {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\index.js", [], [], "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\App.js", [], [], "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\components\\common\\ProtectedRoute.js", [], [], "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\components\\layout\\Layout.js", [], [], "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\features\\dashboard\\Dashboard.js", [], [], "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\features\\auth\\Login.js", [], [], "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\features\\auth\\Register.js", [], [], "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\features\\admin\\AdminDashboard.js", [], [], "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\features\\workshop\\WorkshopPage.js", ["127", "128"], [], "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\features\\daily-services\\DailyServicesPage.js", ["129", "130"], [], "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\features\\psychology\\pages\\DoctorBiography.js", ["131", "132", "133", "134", "135", "136", "137"], [], "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\features\\psychology\\pages\\ConsultationDetail.js", ["138", "139"], [], "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\features\\psychology\\pages\\PsychologyPage.js", [], [], "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\components\\layout\\Header.js", [], [], "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\components\\layout\\Footer.js", ["140", "141", "142", "143", "144", "145", "146", "147", "148", "149", "150", "151"], [], "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\services\\authService.js", [], [], "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\components\\ui\\BackButton.js", [], [], "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\services\\api.js", [], [], "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\features\\dashboard\\components\\ConsultationSection.js", [], [], "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\features\\dashboard\\components\\HeroSection.js", [], [], "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\features\\dashboard\\components\\ServicesSection.js", [], [], "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\features\\dashboard\\components\\AboutSection.js", [], [], "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\features\\dashboard\\components\\GuaranteesSection.js", [], [], "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\features\\dashboard\\components\\WhyUsSection.js", [], [], {"ruleId": "152", "severity": 1, "message": "153", "line": 10, "column": 10, "nodeType": "154", "messageId": "155", "endLine": 10, "endColumn": 22}, {"ruleId": "152", "severity": 1, "message": "156", "line": 10, "column": 24, "nodeType": "154", "messageId": "155", "endLine": 10, "endColumn": 39}, {"ruleId": "152", "severity": 1, "message": "157", "line": 2, "column": 46, "nodeType": "154", "messageId": "155", "endLine": 2, "endColumn": 57}, {"ruleId": "152", "severity": 1, "message": "158", "line": 6, "column": 9, "nodeType": "154", "messageId": "155", "endLine": 6, "endColumn": 17}, {"ruleId": "152", "severity": 1, "message": "159", "line": 3, "column": 40, "nodeType": "154", "messageId": "155", "endLine": 3, "endColumn": 51}, {"ruleId": "160", "severity": 1, "message": "161", "line": 60, "column": 15, "nodeType": "162", "endLine": 60, "endColumn": 73}, {"ruleId": "160", "severity": 1, "message": "161", "line": 61, "column": 15, "nodeType": "162", "endLine": 61, "endColumn": 73}, {"ruleId": "160", "severity": 1, "message": "161", "line": 62, "column": 15, "nodeType": "162", "endLine": 62, "endColumn": 73}, {"ruleId": "160", "severity": 1, "message": "161", "line": 164, "column": 17, "nodeType": "162", "endLine": 164, "endColumn": 72}, {"ruleId": "160", "severity": 1, "message": "161", "line": 165, "column": 17, "nodeType": "162", "endLine": 165, "endColumn": 72}, {"ruleId": "160", "severity": 1, "message": "161", "line": 166, "column": 17, "nodeType": "162", "endLine": 166, "endColumn": 72}, {"ruleId": "152", "severity": 1, "message": "163", "line": 3, "column": 23, "nodeType": "154", "messageId": "155", "endLine": 3, "endColumn": 29}, {"ruleId": "152", "severity": 1, "message": "164", "line": 3, "column": 55, "nodeType": "154", "messageId": "155", "endLine": 3, "endColumn": 70}, {"ruleId": "160", "severity": 1, "message": "161", "line": 17, "column": 15, "nodeType": "162", "endLine": 17, "endColumn": 91}, {"ruleId": "160", "severity": 1, "message": "161", "line": 20, "column": 15, "nodeType": "162", "endLine": 20, "endColumn": 91}, {"ruleId": "160", "severity": 1, "message": "161", "line": 23, "column": 15, "nodeType": "162", "endLine": 23, "endColumn": 91}, {"ruleId": "160", "severity": 1, "message": "161", "line": 26, "column": 15, "nodeType": "162", "endLine": 26, "endColumn": 91}, {"ruleId": "160", "severity": 1, "message": "161", "line": 64, "column": 17, "nodeType": "162", "endLine": 64, "endColumn": 90}, {"ruleId": "160", "severity": 1, "message": "161", "line": 69, "column": 17, "nodeType": "162", "endLine": 69, "endColumn": 90}, {"ruleId": "160", "severity": 1, "message": "161", "line": 74, "column": 17, "nodeType": "162", "endLine": 74, "endColumn": 90}, {"ruleId": "160", "severity": 1, "message": "161", "line": 79, "column": 17, "nodeType": "162", "endLine": 79, "endColumn": 90}, {"ruleId": "160", "severity": 1, "message": "161", "line": 84, "column": 17, "nodeType": "162", "endLine": 84, "endColumn": 90}, {"ruleId": "160", "severity": 1, "message": "161", "line": 121, "column": 15, "nodeType": "162", "endLine": 121, "endColumn": 96}, {"ruleId": "160", "severity": 1, "message": "161", "line": 124, "column": 15, "nodeType": "162", "endLine": 124, "endColumn": 96}, {"ruleId": "160", "severity": 1, "message": "161", "line": 127, "column": 15, "nodeType": "162", "endLine": 127, "endColumn": 96}, "no-unused-vars", "'showProducts' is assigned a value but never used.", "Identifier", "unusedVar", "'setShowProducts' is assigned a value but never used.", "'FaArrowLeft' is defined but never used.", "'navigate' is assigned a value but never used.", "'FaBriefcase' is defined but never used.", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "'FaUser' is defined but never used.", "'FaMoneyBillWave' is defined but never used."]