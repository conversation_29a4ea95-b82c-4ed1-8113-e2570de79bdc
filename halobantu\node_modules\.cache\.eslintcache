[{"C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\index.js": "1", "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\App.js": "2", "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\components\\common\\ProtectedRoute.js": "4", "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\components\\layout\\Layout.js": "5", "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\features\\dashboard\\Dashboard.js": "6", "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\features\\auth\\Login.js": "7", "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\features\\auth\\Register.js": "8", "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\features\\admin\\AdminDashboard.js": "9", "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\features\\workshop\\WorkshopPage.js": "10", "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\features\\daily-services\\DailyServicesPage.js": "11", "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\features\\psychology\\pages\\DoctorBiography.js": "12", "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\features\\psychology\\pages\\ConsultationDetail.js": "13", "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\features\\psychology\\pages\\PsychologyPage.js": "14", "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\components\\layout\\Header.js": "15", "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\components\\layout\\Footer.js": "16", "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\services\\authService.js": "17", "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\components\\ui\\BackButton.js": "18", "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\services\\api.js": "19", "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\components\\TestPage.js": "20"}, {"size": 542, "mtime": 1749559665369, "results": "21", "hashOfConfig": "22"}, {"size": 3133, "mtime": 1749634152823, "results": "23", "hashOfConfig": "22"}, {"size": 362, "mtime": 1749483836823, "results": "24", "hashOfConfig": "22"}, {"size": 1220, "mtime": 1749634096415, "results": "25", "hashOfConfig": "22"}, {"size": 327, "mtime": 1749558261407, "results": "26", "hashOfConfig": "22"}, {"size": 6845, "mtime": 1749632150842, "results": "27", "hashOfConfig": "22"}, {"size": 9710, "mtime": 1749634079708, "results": "28", "hashOfConfig": "22"}, {"size": 11226, "mtime": 1749631759537, "results": "29", "hashOfConfig": "22"}, {"size": 6741, "mtime": 1749632274617, "results": "30", "hashOfConfig": "22"}, {"size": 16432, "mtime": 1749563193810, "results": "31", "hashOfConfig": "22"}, {"size": 7073, "mtime": 1749558443270, "results": "32", "hashOfConfig": "22"}, {"size": 7484, "mtime": 1749563767075, "results": "33", "hashOfConfig": "22"}, {"size": 5020, "mtime": 1749558483627, "results": "34", "hashOfConfig": "22"}, {"size": 12433, "mtime": 1749563625653, "results": "35", "hashOfConfig": "22"}, {"size": 4614, "mtime": 1749558219576, "results": "36", "hashOfConfig": "22"}, {"size": 5335, "mtime": 1749558250334, "results": "37", "hashOfConfig": "22"}, {"size": 2736, "mtime": 1749634064671, "results": "38", "hashOfConfig": "22"}, {"size": 680, "mtime": 1749500724181, "results": "39", "hashOfConfig": "22"}, {"size": 1032, "mtime": 1749631537985, "results": "40", "hashOfConfig": "22"}, {"size": 2975, "mtime": 1749634131049, "results": "41", "hashOfConfig": "22"}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1afa335", {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\index.js", [], [], "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\App.js", [], [], "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\components\\common\\ProtectedRoute.js", [], [], "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\components\\layout\\Layout.js", [], [], "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\features\\dashboard\\Dashboard.js", [], [], "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\features\\auth\\Login.js", [], [], "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\features\\auth\\Register.js", [], [], "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\features\\admin\\AdminDashboard.js", [], [], "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\features\\workshop\\WorkshopPage.js", ["102", "103"], [], "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\features\\daily-services\\DailyServicesPage.js", ["104", "105"], [], "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\features\\psychology\\pages\\DoctorBiography.js", ["106", "107", "108", "109", "110", "111", "112"], [], "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\features\\psychology\\pages\\ConsultationDetail.js", ["113", "114"], [], "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\features\\psychology\\pages\\PsychologyPage.js", [], [], "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\components\\layout\\Header.js", [], [], "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\components\\layout\\Footer.js", ["115", "116", "117", "118", "119", "120", "121", "122", "123", "124", "125", "126"], [], "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\services\\authService.js", [], [], "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\components\\ui\\BackButton.js", [], [], "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\services\\api.js", [], [], "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\components\\TestPage.js", [], [], {"ruleId": "127", "severity": 1, "message": "128", "line": 10, "column": 10, "nodeType": "129", "messageId": "130", "endLine": 10, "endColumn": 22}, {"ruleId": "127", "severity": 1, "message": "131", "line": 10, "column": 24, "nodeType": "129", "messageId": "130", "endLine": 10, "endColumn": 39}, {"ruleId": "127", "severity": 1, "message": "132", "line": 2, "column": 46, "nodeType": "129", "messageId": "130", "endLine": 2, "endColumn": 57}, {"ruleId": "127", "severity": 1, "message": "133", "line": 6, "column": 9, "nodeType": "129", "messageId": "130", "endLine": 6, "endColumn": 17}, {"ruleId": "127", "severity": 1, "message": "134", "line": 3, "column": 40, "nodeType": "129", "messageId": "130", "endLine": 3, "endColumn": 51}, {"ruleId": "135", "severity": 1, "message": "136", "line": 60, "column": 15, "nodeType": "137", "endLine": 60, "endColumn": 73}, {"ruleId": "135", "severity": 1, "message": "136", "line": 61, "column": 15, "nodeType": "137", "endLine": 61, "endColumn": 73}, {"ruleId": "135", "severity": 1, "message": "136", "line": 62, "column": 15, "nodeType": "137", "endLine": 62, "endColumn": 73}, {"ruleId": "135", "severity": 1, "message": "136", "line": 164, "column": 17, "nodeType": "137", "endLine": 164, "endColumn": 72}, {"ruleId": "135", "severity": 1, "message": "136", "line": 165, "column": 17, "nodeType": "137", "endLine": 165, "endColumn": 72}, {"ruleId": "135", "severity": 1, "message": "136", "line": 166, "column": 17, "nodeType": "137", "endLine": 166, "endColumn": 72}, {"ruleId": "127", "severity": 1, "message": "138", "line": 3, "column": 23, "nodeType": "129", "messageId": "130", "endLine": 3, "endColumn": 29}, {"ruleId": "127", "severity": 1, "message": "139", "line": 3, "column": 55, "nodeType": "129", "messageId": "130", "endLine": 3, "endColumn": 70}, {"ruleId": "135", "severity": 1, "message": "136", "line": 17, "column": 15, "nodeType": "137", "endLine": 17, "endColumn": 91}, {"ruleId": "135", "severity": 1, "message": "136", "line": 20, "column": 15, "nodeType": "137", "endLine": 20, "endColumn": 91}, {"ruleId": "135", "severity": 1, "message": "136", "line": 23, "column": 15, "nodeType": "137", "endLine": 23, "endColumn": 91}, {"ruleId": "135", "severity": 1, "message": "136", "line": 26, "column": 15, "nodeType": "137", "endLine": 26, "endColumn": 91}, {"ruleId": "135", "severity": 1, "message": "136", "line": 64, "column": 17, "nodeType": "137", "endLine": 64, "endColumn": 90}, {"ruleId": "135", "severity": 1, "message": "136", "line": 69, "column": 17, "nodeType": "137", "endLine": 69, "endColumn": 90}, {"ruleId": "135", "severity": 1, "message": "136", "line": 74, "column": 17, "nodeType": "137", "endLine": 74, "endColumn": 90}, {"ruleId": "135", "severity": 1, "message": "136", "line": 79, "column": 17, "nodeType": "137", "endLine": 79, "endColumn": 90}, {"ruleId": "135", "severity": 1, "message": "136", "line": 84, "column": 17, "nodeType": "137", "endLine": 84, "endColumn": 90}, {"ruleId": "135", "severity": 1, "message": "136", "line": 121, "column": 15, "nodeType": "137", "endLine": 121, "endColumn": 96}, {"ruleId": "135", "severity": 1, "message": "136", "line": 124, "column": 15, "nodeType": "137", "endLine": 124, "endColumn": 96}, {"ruleId": "135", "severity": 1, "message": "136", "line": 127, "column": 15, "nodeType": "137", "endLine": 127, "endColumn": 96}, "no-unused-vars", "'showProducts' is assigned a value but never used.", "Identifier", "unusedVar", "'setShowProducts' is assigned a value but never used.", "'FaArrowLeft' is defined but never used.", "'navigate' is assigned a value but never used.", "'FaBriefcase' is defined but never used.", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "'FaUser' is defined but never used.", "'FaMoneyBillWave' is defined but never used."]