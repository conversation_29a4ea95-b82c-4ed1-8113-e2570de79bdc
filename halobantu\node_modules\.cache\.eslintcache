[{"C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\index.js": "1", "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\App.js": "2", "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\components\\common\\ProtectedRoute.js": "4", "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\components\\layout\\Layout.js": "5", "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\features\\dashboard\\Dashboard.js": "6", "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\features\\auth\\Login.js": "7", "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\features\\auth\\Register.js": "8", "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\features\\admin\\AdminDashboard.js": "9", "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\features\\workshop\\WorkshopPage.js": "10", "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\features\\daily-services\\DailyServicesPage.js": "11", "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\features\\psychology\\pages\\DoctorBiography.js": "12", "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\features\\psychology\\pages\\ConsultationDetail.js": "13", "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\features\\psychology\\pages\\PsychologyPage.js": "14", "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\components\\layout\\Header.js": "15", "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\components\\layout\\Footer.js": "16", "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\services\\authService.js": "17", "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\components\\ui\\BackButton.js": "18", "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\services\\api.js": "19"}, {"size": 542, "mtime": 1749559665369, "results": "20", "hashOfConfig": "21"}, {"size": 2960, "mtime": 1749632316230, "results": "22", "hashOfConfig": "21"}, {"size": 362, "mtime": 1749483836823, "results": "23", "hashOfConfig": "21"}, {"size": 805, "mtime": 1749632108266, "results": "24", "hashOfConfig": "21"}, {"size": 327, "mtime": 1749558261407, "results": "25", "hashOfConfig": "21"}, {"size": 6845, "mtime": 1749632150842, "results": "26", "hashOfConfig": "21"}, {"size": 9271, "mtime": 1749632091772, "results": "27", "hashOfConfig": "21"}, {"size": 11226, "mtime": 1749631759537, "results": "28", "hashOfConfig": "21"}, {"size": 6741, "mtime": 1749632274617, "results": "29", "hashOfConfig": "21"}, {"size": 16432, "mtime": 1749563193810, "results": "30", "hashOfConfig": "21"}, {"size": 7073, "mtime": 1749558443270, "results": "31", "hashOfConfig": "21"}, {"size": 7484, "mtime": 1749563767075, "results": "32", "hashOfConfig": "21"}, {"size": 5020, "mtime": 1749558483627, "results": "33", "hashOfConfig": "21"}, {"size": 12433, "mtime": 1749563625653, "results": "34", "hashOfConfig": "21"}, {"size": 4614, "mtime": 1749558219576, "results": "35", "hashOfConfig": "21"}, {"size": 5335, "mtime": 1749558250334, "results": "36", "hashOfConfig": "21"}, {"size": 2433, "mtime": 1749631621811, "results": "37", "hashOfConfig": "21"}, {"size": 680, "mtime": 1749500724181, "results": "38", "hashOfConfig": "21"}, {"size": 1032, "mtime": 1749631537985, "results": "39", "hashOfConfig": "21"}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1afa335", {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\index.js", [], [], "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\App.js", [], [], "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\components\\common\\ProtectedRoute.js", [], [], "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\components\\layout\\Layout.js", [], [], "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\features\\dashboard\\Dashboard.js", [], [], "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\features\\auth\\Login.js", [], [], "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\features\\auth\\Register.js", [], [], "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\features\\admin\\AdminDashboard.js", [], [], "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\features\\workshop\\WorkshopPage.js", ["97", "98"], [], "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\features\\daily-services\\DailyServicesPage.js", ["99", "100"], [], "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\features\\psychology\\pages\\DoctorBiography.js", ["101", "102", "103", "104", "105", "106", "107"], [], "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\features\\psychology\\pages\\ConsultationDetail.js", ["108", "109"], [], "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\features\\psychology\\pages\\PsychologyPage.js", [], [], "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\components\\layout\\Header.js", [], [], "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\components\\layout\\Footer.js", ["110", "111", "112", "113", "114", "115", "116", "117", "118", "119", "120", "121"], [], "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\services\\authService.js", [], [], "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\components\\ui\\BackButton.js", [], [], "C:\\Users\\<USER>\\Documents\\JOKI MAKALAH\\halobantu1\\halobantu\\src\\services\\api.js", [], [], {"ruleId": "122", "severity": 1, "message": "123", "line": 10, "column": 10, "nodeType": "124", "messageId": "125", "endLine": 10, "endColumn": 22}, {"ruleId": "122", "severity": 1, "message": "126", "line": 10, "column": 24, "nodeType": "124", "messageId": "125", "endLine": 10, "endColumn": 39}, {"ruleId": "122", "severity": 1, "message": "127", "line": 2, "column": 46, "nodeType": "124", "messageId": "125", "endLine": 2, "endColumn": 57}, {"ruleId": "122", "severity": 1, "message": "128", "line": 6, "column": 9, "nodeType": "124", "messageId": "125", "endLine": 6, "endColumn": 17}, {"ruleId": "122", "severity": 1, "message": "129", "line": 3, "column": 40, "nodeType": "124", "messageId": "125", "endLine": 3, "endColumn": 51}, {"ruleId": "130", "severity": 1, "message": "131", "line": 60, "column": 15, "nodeType": "132", "endLine": 60, "endColumn": 73}, {"ruleId": "130", "severity": 1, "message": "131", "line": 61, "column": 15, "nodeType": "132", "endLine": 61, "endColumn": 73}, {"ruleId": "130", "severity": 1, "message": "131", "line": 62, "column": 15, "nodeType": "132", "endLine": 62, "endColumn": 73}, {"ruleId": "130", "severity": 1, "message": "131", "line": 164, "column": 17, "nodeType": "132", "endLine": 164, "endColumn": 72}, {"ruleId": "130", "severity": 1, "message": "131", "line": 165, "column": 17, "nodeType": "132", "endLine": 165, "endColumn": 72}, {"ruleId": "130", "severity": 1, "message": "131", "line": 166, "column": 17, "nodeType": "132", "endLine": 166, "endColumn": 72}, {"ruleId": "122", "severity": 1, "message": "133", "line": 3, "column": 23, "nodeType": "124", "messageId": "125", "endLine": 3, "endColumn": 29}, {"ruleId": "122", "severity": 1, "message": "134", "line": 3, "column": 55, "nodeType": "124", "messageId": "125", "endLine": 3, "endColumn": 70}, {"ruleId": "130", "severity": 1, "message": "131", "line": 17, "column": 15, "nodeType": "132", "endLine": 17, "endColumn": 91}, {"ruleId": "130", "severity": 1, "message": "131", "line": 20, "column": 15, "nodeType": "132", "endLine": 20, "endColumn": 91}, {"ruleId": "130", "severity": 1, "message": "131", "line": 23, "column": 15, "nodeType": "132", "endLine": 23, "endColumn": 91}, {"ruleId": "130", "severity": 1, "message": "131", "line": 26, "column": 15, "nodeType": "132", "endLine": 26, "endColumn": 91}, {"ruleId": "130", "severity": 1, "message": "131", "line": 64, "column": 17, "nodeType": "132", "endLine": 64, "endColumn": 90}, {"ruleId": "130", "severity": 1, "message": "131", "line": 69, "column": 17, "nodeType": "132", "endLine": 69, "endColumn": 90}, {"ruleId": "130", "severity": 1, "message": "131", "line": 74, "column": 17, "nodeType": "132", "endLine": 74, "endColumn": 90}, {"ruleId": "130", "severity": 1, "message": "131", "line": 79, "column": 17, "nodeType": "132", "endLine": 79, "endColumn": 90}, {"ruleId": "130", "severity": 1, "message": "131", "line": 84, "column": 17, "nodeType": "132", "endLine": 84, "endColumn": 90}, {"ruleId": "130", "severity": 1, "message": "131", "line": 121, "column": 15, "nodeType": "132", "endLine": 121, "endColumn": 96}, {"ruleId": "130", "severity": 1, "message": "131", "line": 124, "column": 15, "nodeType": "132", "endLine": 124, "endColumn": 96}, {"ruleId": "130", "severity": 1, "message": "131", "line": 127, "column": 15, "nodeType": "132", "endLine": 127, "endColumn": 96}, "no-unused-vars", "'showProducts' is assigned a value but never used.", "Identifier", "unusedVar", "'setShowProducts' is assigned a value but never used.", "'FaArrowLeft' is defined but never used.", "'navigate' is assigned a value but never used.", "'FaBriefcase' is defined but never used.", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "'FaUser' is defined but never used.", "'FaMoneyBillWave' is defined but never used."]