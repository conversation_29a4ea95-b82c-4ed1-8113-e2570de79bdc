const { sequelize } = require('../config/database');
const User = require('../models/User');

const syncDatabase = async () => {
  try {
    console.log('🔄 Testing database connection...');
    await sequelize.authenticate();
    console.log('✅ Database connection established');
    
    console.log('🔄 Synchronizing database models...');
    
    // Sync all models
    await sequelize.sync({ 
      force: process.argv.includes('--force'),
      alter: process.argv.includes('--alter')
    });
    
    console.log('✅ Database models synchronized successfully');
    
    // Create default admin user if it doesn't exist
    const adminEmail = '<EMAIL>';
    const existingAdmin = await User.findByEmail(adminEmail);
    
    if (!existingAdmin) {
      console.log('🔄 Creating default admin user...');
      
      const adminUser = await User.create({
        email: adminEmail,
        password: 'Admin123!',
        firstName: 'Admin',
        lastName: 'HaloBantu',
        role: 'admin',
        isActive: true,
        emailVerified: true
      });
      
      console.log('✅ Default admin user created:');
      console.log(`   Email: ${adminEmail}`);
      console.log(`   Password: Admin123!`);
      console.log('   ⚠️  Please change the password after first login!');
    } else {
      console.log('ℹ️  Admin user already exists');
    }
    
    console.log('🎉 Database synchronization completed!');
    
  } catch (error) {
    console.error('❌ Error synchronizing database:', error);
    process.exit(1);
  } finally {
    await sequelize.close();
    console.log('🔌 Database connection closed');
  }
};

// Run if called directly
if (require.main === module) {
  syncDatabase();
}

module.exports = syncDatabase;
