import React from 'react';
import { FaClock, FaShieldAlt, FaPhoneAlt, FaCheckCircle } from 'react-icons/fa';

const GuaranteesSection = () => {
  const guarantees = [
    {
      icon: <FaClock className="text-5xl text-blue-600" />,
      title: "24/7 Support",
      subtitle: "Dukungan Sepanjang Waktu",
      description: "Tim support teknologi Future X siap membantu Anda kapan saja dengan solusi cerdas dan responsif.",
      features: [
        "Respon instan dengan AI support",
        "Multi-channel: Chat, call, email",
        "Tim ahli teknologi otomotif",
        "Emergency support 24/7"
      ],
      bgColor: "from-blue-500 to-blue-600"
    },
    {
      icon: <FaShieldAlt className="text-5xl text-green-600" />,
      title: "100% Garansi",
      subtitle: "Kepuasan Terjamin",
      description: "Future X memberikan jaminan kualitas premium dengan standar internasional untuk kepuasan maksimal.",
      features: [
        "Garansi teknologi terdepan",
        "Standar kualitas internasional",
        "Sertifikasi resmi",
        "Warranty seumur hidup"
      ],
      bgColor: "from-green-500 to-green-600"
    }
  ];

  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-4">
            Jaminan Kami
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Komitmen Future X untuk menghadirkan teknologi terdepan dengan jaminan kualitas dan kepuasan pelanggan
          </p>
        </div>

        {/* Guarantees Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-6xl mx-auto">
          {guarantees.map((guarantee, index) => (
            <div
              key={index}
              className="relative bg-white rounded-2xl shadow-xl overflow-hidden group hover:shadow-2xl transition-all duration-300"
            >
              {/* Background Gradient */}
              <div className={`absolute inset-0 bg-gradient-to-br ${guarantee.bgColor} opacity-5 group-hover:opacity-10 transition-opacity duration-300`}></div>
              
              <div className="relative p-8">
                {/* Icon and Title */}
                <div className="text-center mb-6">
                  <div className="inline-flex items-center justify-center w-20 h-20 bg-gray-50 rounded-full mb-4 group-hover:scale-110 transition-transform duration-300">
                    {guarantee.icon}
                  </div>
                  <h3 className="text-3xl font-bold text-gray-800 mb-2">
                    {guarantee.title}
                  </h3>
                  <p className="text-lg text-gray-600 font-medium">
                    {guarantee.subtitle}
                  </p>
                </div>

                {/* Description */}
                <p className="text-gray-600 text-center mb-8 leading-relaxed">
                  {guarantee.description}
                </p>

                {/* Features List */}
                <div className="space-y-3">
                  {guarantee.features.map((feature, featureIndex) => (
                    <div key={featureIndex} className="flex items-center space-x-3">
                      <FaCheckCircle className="text-green-500 text-lg flex-shrink-0" />
                      <span className="text-gray-700">{feature}</span>
                    </div>
                  ))}
                </div>

                {/* CTA Button */}
                <div className="text-center mt-8">
                  <button className={`px-8 py-3 bg-gradient-to-r ${guarantee.bgColor} text-white font-semibold rounded-lg hover:shadow-lg transition-all duration-300 transform hover:scale-105`}>
                    {index === 0 ? 'Hubungi Support' : 'Pelajari Garansi'}
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Contact Info */}
        <div className="text-center mt-16">
          <div className="bg-gray-50 rounded-2xl p-8 max-w-4xl mx-auto">
            <div className="flex items-center justify-center mb-4">
              <FaPhoneAlt className="text-3xl text-blue-600 mr-3" />
              <h3 className="text-2xl font-bold text-gray-800">
                Butuh Bantuan Segera?
              </h3>
            </div>
            <p className="text-lg text-gray-600 mb-6">
              Tim teknologi Future X siap memberikan solusi inovatif dengan respon cepat dan akurat
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a 
                href="tel:+6281234567890"
                className="px-8 py-4 bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-lg transition-all duration-300 transform hover:scale-105 flex items-center justify-center"
              >
                <FaPhoneAlt className="mr-2" />
                Telepon Sekarang
              </a>
              <a 
                href="https://wa.me/6281234567890"
                className="px-8 py-4 bg-green-600 hover:bg-green-700 text-white font-semibold rounded-lg transition-all duration-300 transform hover:scale-105"
              >
                WhatsApp
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default GuaranteesSection;
