import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { FaChevronDown } from 'react-icons/fa';
import BackButton from '../../../components/ui/BackButton';

const PsychologyPage = () => {
  const navigate = useNavigate();
  const [bookingForm, setBookingForm] = useState({
    doctor: '',
    date: '',
    time: '',
    duration: ''
  });

  const psychologists = [
    {
      id: 1,
      name: 'Dr. <PERSON><PERSON><PERSON>',
      specialization: 'Pengalaman 4 tahun',
      rating: 4.8,
      image: 'https://images.pexels.com/photos/5327921/pexels-photo-5327921.jpeg?auto=compress&cs=tinysrgb&w=400',
      bio: 'Kami siap mendampingi perjalanan Anda menuju kesehatan emosional. Dapatkan bimbingan dari psikolog profesional dan temukan solusi terbaik untuk kesehatan mental Anda.',
      availableHours: 20,
      clinic: 'Klinik ABC Surabaya Utara',
      education: 'Universitas Airlangga, 2015',
      experience: [
        'Psikolog Klinis di RS Medika (2018-sekarang)',
        'Konsultan di Pusat Kesehatan Mental (2015-2018)',
        'Peneliti di Institut Psikologi Nasional (2012-2015)'
      ],
      educationDetail: [
        'Ph.D. in Clinical Psychology, Universitas Indonesia',
        'M.Psi., Psikologi Klinis, Universitas Gadjah Mada',
        'S.Psi., Psikologi, Universitas Indonesia'
      ]
    },
    {
      id: 2,
      name: 'Dr. Rizki Ramadhina',
      specialization: 'Pengalaman 5 tahun',
      rating: 4.9,
      image: 'https://images.pexels.com/photos/5327656/pexels-photo-5327656.jpeg?auto=compress&cs=tinysrgb&w=400',
      bio: 'Kami siap mendampingi perjalanan Anda menuju kesehatan emosional. Dapatkan bimbingan dari psikolog profesional dan temukan solusi terbaik untuk kesehatan mental Anda.',
      availableHours: 15,
      clinic: 'Klinik DEF Jakarta Selatan',
      education: 'Universitas Padjadjaran, 2016',
      experience: [
        'Psikolog Anak di Klinik Anak Sejahtera (2019-sekarang)',
        'Konsultan Sekolah di Sekolah Internasional (2016-2019)',
        'Terapis Anak di Pusat Tumbuh Kembang (2014-2016)'
      ],
      educationDetail: [
        'Ph.D. in Child Psychology, Universitas Airlangga',
        'M.Psi., Psikologi Anak, Universitas Padjadjaran',
        'S.Psi., Psikologi, Universitas Indonesia'
      ]
    },
    {
      id: 3,
      name: 'Dr. Sukma Susita',
      specialization: 'Pengalaman 5 tahun',
      rating: 4.7,
      image: 'https://images.pexels.com/photos/5327585/pexels-photo-5327585.jpeg?auto=compress&cs=tinysrgb&w=400',
      bio: 'Kami siap mendampingi perjalanan Anda menuju kesehatan emosional. Dapatkan bimbingan dari psikolog profesional dan temukan solusi terbaik untuk kesehatan mental Anda.',
      availableHours: 18,
      clinic: 'Klinik GHI Bandung Utara',
      education: 'Universitas Gadjah Mada, 2017',
      experience: [
        'Psikolog Keluarga di Pusat Konseling Keluarga (2017-sekarang)',
        'Konselor di Lembaga Bantuan Hukum (2015-2017)',
        'Terapis Keluarga di Klinik Psikologi (2013-2015)'
      ],
      educationDetail: [
        'Ph.D. in Family Psychology, Universitas Gadjah Mada',
        'M.Psi., Psikologi Keluarga, Universitas Indonesia',
        'S.Psi., Psikologi, Universitas Padjadjaran'
      ]
    },
    {
      id: 4,
      name: 'Dr. Ayman Alatas',
      specialization: 'Pengalaman 5 tahun',
      rating: 4.6,
      image: 'https://images.pexels.com/photos/5327580/pexels-photo-5327580.jpeg?auto=compress&cs=tinysrgb&w=400',
      bio: 'Kami siap mendampingi perjalanan Anda menuju kesehatan emosional. Dapatkan bimbingan dari psikolog profesional dan temukan solusi terbaik untuk kesehatan mental Anda.',
      availableHours: 22,
      clinic: 'Klinik JKL Medan Timur',
      education: 'Universitas Padjadjaran, 2018',
      experience: [
        'Psikolog Klinis di Rumah Sakit Jiwa (2019-sekarang)',
        'Terapis CBT di Klinik Psikologi (2016-2019)',
        'Konselor di Pusat Rehabilitasi (2014-2016)'
      ],
      educationDetail: [
        'Ph.D. in Clinical Psychology, Universitas Padjadjaran',
        'M.Psi., Psikologi Klinis, Universitas Indonesia',
        'S.Psi., Psikologi, Universitas Gadjah Mada'
      ]
    }
  ];

  const timeSlots = [
    '09:00 - 10:00',
    '10:00 - 11:00',
    '11:00 - 12:00',
    '13:00 - 14:00',
    '14:00 - 15:00',
    '15:00 - 16:00',
    '16:00 - 17:00'
  ];

  const durations = [
    '30 menit',
    '45 menit',
    '60 menit',
    '90 menit'
  ];

  const handleScheduleSelect = (psychologistWithSchedule) => {
    navigate('/psychology/consultation', { state: { psychologist: psychologistWithSchedule } });
  };

  const handleViewBiography = (doctor) => {
    navigate(`/psychology/doctor/${doctor.id}`, { state: { doctor } });
  };

  const handleBookingSubmit = (e) => {
    e.preventDefault();
    if (bookingForm.doctor && bookingForm.date && bookingForm.time && bookingForm.duration) {
      alert(`Booking berhasil!\nDokter: ${bookingForm.doctor}\nTanggal: ${bookingForm.date}\nWaktu: ${bookingForm.time}\nDurasi: ${bookingForm.duration}`);
    } else {
      alert('Mohon lengkapi semua field booking');
    }
  };

  const handleInputChange = (field, value) => {
    setBookingForm(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8 relative">
      <div className="max-w-7xl mx-auto px-4 pt-16">
        <BackButton />

        {/* Booking Form Section */}
        <div className="bg-white rounded-xl shadow-lg p-8 mb-12">
          <h2 className="text-2xl font-bold text-gray-800 text-center mb-8">
            Booking Konsultasi Psikologi
          </h2>

          <form onSubmit={handleBookingSubmit} className="max-w-4xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              {/* Pilih Dokter Konsultasi */}
              <div className="relative">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Pilih Dokter Konsultasi
                </label>
                <div className="relative">
                  <select
                    value={bookingForm.doctor}
                    onChange={(e) => handleInputChange('doctor', e.target.value)}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg appearance-none bg-white focus:outline-none focus:border-blue-500 transition-colors"
                  >
                    <option value="">Pilih Dokter</option>
                    {psychologists.map((doctor) => (
                      <option key={doctor.id} value={doctor.name}>
                        {doctor.name}
                      </option>
                    ))}
                  </select>
                  <FaChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none" />
                </div>
              </div>

              {/* Tanggal Konsultasi */}
              <div className="relative">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Pilih Jam Konsultasi
                </label>
                <div className="relative">
                  <input
                    type="date"
                    value={bookingForm.date}
                    onChange={(e) => handleInputChange('date', e.target.value)}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500 transition-colors"
                  />
                </div>
              </div>

              {/* Pilih Jam Konsultasi */}
              <div className="relative">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Durasi Konsultasi
                </label>
                <div className="relative">
                  <select
                    value={bookingForm.time}
                    onChange={(e) => handleInputChange('time', e.target.value)}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg appearance-none bg-white focus:outline-none focus:border-blue-500 transition-colors"
                  >
                    <option value="">Pilih Jam</option>
                    {timeSlots.map((time) => (
                      <option key={time} value={time}>
                        {time}
                      </option>
                    ))}
                  </select>
                  <FaChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none" />
                </div>
              </div>

              {/* Durasi Konsultasi */}
              <div className="relative">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Durasi Konsultasi
                </label>
                <div className="relative">
                  <select
                    value={bookingForm.duration}
                    onChange={(e) => handleInputChange('duration', e.target.value)}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg appearance-none bg-white focus:outline-none focus:border-blue-500 transition-colors"
                  >
                    <option value="">Pilih Durasi</option>
                    {durations.map((duration) => (
                      <option key={duration} value={duration}>
                        {duration}
                      </option>
                    ))}
                  </select>
                  <FaChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none" />
                </div>
              </div>
            </div>

            {/* Booking Button */}
            <div className="text-center">
              <button
                type="submit"
                className="bg-blue-600 text-white px-12 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors duration-200 text-lg"
              >
                Booking
              </button>
            </div>
          </form>
        </div>

        {/* Dokter Psikologi Section */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold text-gray-800 text-center mb-8">
            Dokter Psikologi
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {psychologists.map((psychologist) => (
              <div key={psychologist.id} className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                {/* Doctor Image */}
                <div className="relative">
                  <img
                    src={psychologist.image}
                    alt={psychologist.name}
                    className="w-full h-48 object-cover"
                  />
                </div>

                {/* Doctor Info */}
                <div className="p-6 text-center">
                  <h3 className="text-lg font-bold text-gray-800 mb-2">{psychologist.name}</h3>
                  <p className="text-gray-600 text-sm mb-4">{psychologist.specialization}</p>

                  {/* Action Buttons */}
                  <div className="space-y-2">
                    <button
                      onClick={() => handleViewBiography(psychologist)}
                      className="w-full bg-gray-600 text-white py-2 rounded-lg hover:bg-gray-700 transition-colors duration-200 font-medium"
                    >
                      Lihat Biografi
                    </button>
                    <button
                      onClick={() => handleScheduleSelect(psychologist)}
                      className="w-full bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 transition-colors duration-200 font-medium"
                    >
                      Lihat Jadwal
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default PsychologyPage;