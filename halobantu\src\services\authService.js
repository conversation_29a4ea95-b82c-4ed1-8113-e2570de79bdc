import api from './api';

// Auth service untuk komunikasi dengan backend
export const authService = {
  // <PERSON>gin dengan email/username dan password
  login: async (credentials) => {
    try {
      console.log('🔄 Attempting login with:', credentials);
      const response = await api.post('/auth/login', credentials);
      console.log('✅ Login response:', response.data);

      // Simpan token dan user data ke localStorage
      if (response.data.success) {
        const { token, refreshToken, user } = response.data.data;
        console.log('👤 User data:', user);
        console.log('🔑 Token:', token);

        localStorage.setItem('token', token);
        localStorage.setItem('refreshToken', refreshToken);
        localStorage.setItem('user', JSON.stringify(user));

        console.log('💾 Data saved to localStorage');
      }

      return response.data;
    } catch (error) {
      console.error('❌ Login error:', error);
      throw error.response?.data || { message: '<PERSON><PERSON> failed' };
    }
  },

  // Register user baru
  register: async (userData) => {
    try {
      const response = await api.post('/auth/register', userData);
      
      // Simpan token dan user data ke localStorage setelah register
      if (response.data.success) {
        const { token, refreshToken, user } = response.data.data;
        localStorage.setItem('token', token);
        localStorage.setItem('refreshToken', refreshToken);
        localStorage.setItem('user', JSON.stringify(user));
      }
      
      return response.data;
    } catch (error) {
      throw error.response?.data || { message: 'Registration failed' };
    }
  },

  // Logout
  logout: async () => {
    try {
      await api.post('/auth/logout');
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // Hapus data dari localStorage
      localStorage.removeItem('token');
      localStorage.removeItem('refreshToken');
      localStorage.removeItem('user');
    }
  },

  // Get user profile
  getProfile: async () => {
    try {
      const response = await api.get('/auth/profile');
      return response.data;
    } catch (error) {
      throw error.response?.data || { message: 'Failed to get profile' };
    }
  },

  // Check if user is logged in
  isAuthenticated: () => {
    const token = localStorage.getItem('token');
    const user = localStorage.getItem('user');
    return !!(token && user);
  },

  // Get current user from localStorage
  getCurrentUser: () => {
    const user = localStorage.getItem('user');
    return user ? JSON.parse(user) : null;
  },

  // Get token from localStorage
  getToken: () => {
    return localStorage.getItem('token');
  }
};

export default authService;
