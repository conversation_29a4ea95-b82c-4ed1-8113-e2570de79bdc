{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\JOKI MAKALAH\\\\halobantu1\\\\halobantu\\\\src\\\\features\\\\dashboard\\\\components\\\\ConsultationSection.js\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ConsultationSection = () => {\n  const handleBooking = () => {\n    // Redirect to psychology page\n    window.location.href = '/psychology';\n  };\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    id: \"contact\",\n    className: \"py-20 bg-gray-100\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center max-w-6xl mx-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 mb-2\",\n            children: \"Butuh dukungan?\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 15,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-4xl md:text-5xl font-bold text-gray-800 mb-4\",\n            children: \"Jadwalkan Konsultasi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 16,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-lg text-gray-600 mb-8\",\n            children: \"dengan psikolog kami hari ini!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 19,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleBooking,\n            className: \"px-8 py-4 bg-black text-white font-semibold rounded-lg hover:bg-gray-800 transition-all duration-300\",\n            children: \"Booking Sekarang\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 23,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 14,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"rounded-2xl overflow-hidden shadow-lg\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: \"https://images.pexels.com/photos/7176026/pexels-photo-7176026.jpeg?auto=compress&cs=tinysrgb&w=800&h=500&fit=crop\",\n              alt: \"Konsultasi Psikologi\",\n              className: \"w-full h-80 object-cover\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 34,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 33,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 12,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 10,\n    columnNumber: 5\n  }, this);\n};\n_c = ConsultationSection;\nexport default ConsultationSection;\nvar _c;\n$RefreshReg$(_c, \"ConsultationSection\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "ConsultationSection", "handleBooking", "window", "location", "href", "id", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "src", "alt", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/JOKI MAKALAH/halobantu1/halobantu/src/features/dashboard/components/ConsultationSection.js"], "sourcesContent": ["import React from 'react';\n\nconst ConsultationSection = () => {\n  const handleBooking = () => {\n    // Redirect to psychology page\n    window.location.href = '/psychology';\n  };\n\n  return (\n    <section id=\"contact\" className=\"py-20 bg-gray-100\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center max-w-6xl mx-auto\">\n          {/* Left Content */}\n          <div>\n            <p className=\"text-gray-600 mb-2\">Butuh dukungan?</p>\n            <h2 className=\"text-4xl md:text-5xl font-bold text-gray-800 mb-4\">\n              <PERSON><PERSON><PERSON><PERSON>\n            </h2>\n            <p className=\"text-lg text-gray-600 mb-8\">\n              dengan psikolog kami hari ini!\n            </p>\n\n            <button\n              onClick={handleBooking}\n              className=\"px-8 py-4 bg-black text-white font-semibold rounded-lg hover:bg-gray-800 transition-all duration-300\"\n            >\n              Booking Sekarang\n            </button>\n          </div>\n\n          {/* Right Image */}\n          <div className=\"relative\">\n            <div className=\"rounded-2xl overflow-hidden shadow-lg\">\n              <img\n                src=\"https://images.pexels.com/photos/7176026/pexels-photo-7176026.jpeg?auto=compress&cs=tinysrgb&w=800&h=500&fit=crop\"\n                alt=\"Konsultasi Psikologi\"\n                className=\"w-full h-80 object-cover\"\n              />\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default ConsultationSection;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;EAChC,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1B;IACAC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,aAAa;EACtC,CAAC;EAED,oBACEL,OAAA;IAASM,EAAE,EAAC,SAAS;IAACC,SAAS,EAAC,mBAAmB;IAAAC,QAAA,eACjDR,OAAA;MAAKO,SAAS,EAAC,wBAAwB;MAAAC,QAAA,eACrCR,OAAA;QAAKO,SAAS,EAAC,uEAAuE;QAAAC,QAAA,gBAEpFR,OAAA;UAAAQ,QAAA,gBACER,OAAA;YAAGO,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACrDZ,OAAA;YAAIO,SAAS,EAAC,mDAAmD;YAAAC,QAAA,EAAC;UAElE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLZ,OAAA;YAAGO,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAE1C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAEJZ,OAAA;YACEa,OAAO,EAAEX,aAAc;YACvBK,SAAS,EAAC,sGAAsG;YAAAC,QAAA,EACjH;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGNZ,OAAA;UAAKO,SAAS,EAAC,UAAU;UAAAC,QAAA,eACvBR,OAAA;YAAKO,SAAS,EAAC,uCAAuC;YAAAC,QAAA,eACpDR,OAAA;cACEc,GAAG,EAAC,mHAAmH;cACvHC,GAAG,EAAC,sBAAsB;cAC1BR,SAAS,EAAC;YAA0B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAACI,EAAA,GA1CIf,mBAAmB;AA4CzB,eAAeA,mBAAmB;AAAC,IAAAe,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}