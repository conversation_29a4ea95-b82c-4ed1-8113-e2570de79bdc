@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
.App {
    @apply text-center;
}

.App-logo {
    @apply h-[40vmin] pointer-events-none;
  }

  .App-header {
    @apply bg-gray-900 min-h-screen flex flex-col items-center justify-center text-[calc(10px+2vmin)] text-white;
  }

  .App-link {
    @apply text-blue-400;
  }
}

@media (prefers-reduced-motion: no-preference) {
  .App-logo {
    animation: App-logo-spin infinite 20s linear;
  }
}

@keyframes App-logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
