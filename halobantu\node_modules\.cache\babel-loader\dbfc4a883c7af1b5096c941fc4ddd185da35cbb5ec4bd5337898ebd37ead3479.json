{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\JOKI MAKALAH\\\\halobantu1\\\\halobantu\\\\src\\\\features\\\\dashboard\\\\Dashboard.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { FaUser, FaSignOutAlt, FaHome, FaChartBar } from 'react-icons/fa';\nimport authService from '../../services/authService';\nimport { useNavigate } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Dashboard = () => {\n  _s();\n  var _currentUser$role;\n  const navigate = useNavigate();\n  const currentUser = authService.getCurrentUser();\n  const handleLogout = async () => {\n    await authService.logout();\n    navigate('/login');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"bg-white shadow-sm border-b\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between items-center h-16\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(FaHome, {\n              className: \"h-8 w-8 text-blue-600 mr-3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 22,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-xl font-semibold text-gray-900\",\n              children: \"HaloBantu - User Dashboard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 23,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 21,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center text-sm text-gray-700\",\n              children: [/*#__PURE__*/_jsxDEV(FaUser, {\n                className: \"h-4 w-4 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 27,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Halo, \", (currentUser === null || currentUser === void 0 ? void 0 : currentUser.firstName) || (currentUser === null || currentUser === void 0 ? void 0 : currentUser.username), \"!\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 28,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"ml-2 px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full\",\n                children: currentUser === null || currentUser === void 0 ? void 0 : currentUser.role\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 29,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 26,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleLogout,\n              className: \"flex items-center text-sm text-red-600 hover:text-red-800 transition-colors\",\n              children: [/*#__PURE__*/_jsxDEV(FaSignOutAlt, {\n                className: \"h-4 w-4 mr-1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 37,\n                columnNumber: 17\n              }, this), \"Logout\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 33,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-4 py-6 sm:px-0\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white overflow-hidden shadow rounded-lg\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-5\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-shrink-0\",\n                  children: /*#__PURE__*/_jsxDEV(FaUser, {\n                    className: \"h-8 w-8 text-blue-600\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 54,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 53,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"ml-5 w-0 flex-1\",\n                  children: /*#__PURE__*/_jsxDEV(\"dl\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"dt\", {\n                      className: \"text-sm font-medium text-gray-500 truncate\",\n                      children: \"Selamat Datang\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 58,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"dd\", {\n                      className: \"text-lg font-medium text-gray-900\",\n                      children: currentUser !== null && currentUser !== void 0 && currentUser.firstName ? `${currentUser.firstName} ${currentUser.lastName}` : currentUser === null || currentUser === void 0 ? void 0 : currentUser.username\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 61,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 57,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 56,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 52,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 51,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 50,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white overflow-hidden shadow rounded-lg\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-5\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-shrink-0\",\n                  children: /*#__PURE__*/_jsxDEV(FaChartBar, {\n                    className: \"h-8 w-8 text-green-600\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 78,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 77,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"ml-5 w-0 flex-1\",\n                  children: /*#__PURE__*/_jsxDEV(\"dl\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"dt\", {\n                      className: \"text-sm font-medium text-gray-500 truncate\",\n                      children: \"Status Akun\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 82,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"dd\", {\n                      className: \"text-lg font-medium text-gray-900\",\n                      children: \"Aktif\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 85,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 81,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 80,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 76,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 75,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white overflow-hidden shadow rounded-lg\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-5\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-shrink-0\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-blue-600 font-semibold text-sm\",\n                      children: currentUser === null || currentUser === void 0 ? void 0 : (_currentUser$role = currentUser.role) === null || _currentUser$role === void 0 ? void 0 : _currentUser$role.charAt(0).toUpperCase()\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 100,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 99,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 98,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"ml-5 w-0 flex-1\",\n                  children: /*#__PURE__*/_jsxDEV(\"dl\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"dt\", {\n                      className: \"text-sm font-medium text-gray-500 truncate\",\n                      children: \"Role\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 107,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"dd\", {\n                      className: \"text-lg font-medium text-gray-900 capitalize\",\n                      children: currentUser === null || currentUser === void 0 ? void 0 : currentUser.role\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 110,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 106,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 105,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 97,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 96,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-8\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white shadow rounded-lg\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"px-4 py-5 sm:p-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg leading-6 font-medium text-gray-900 mb-4\",\n                children: \"Dashboard User\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 124,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 mb-4\",\n                children: \"Selamat datang di dashboard user HaloBantu. Dari sini Anda dapat mengakses berbagai layanan yang tersedia.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 127,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"border border-gray-200 rounded-lg p-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"font-medium text-gray-900 mb-2\",\n                    children: \"Konsultasi Psikologi\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 132,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-600\",\n                    children: \"Akses layanan konsultasi dengan psikolog profesional\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 133,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 131,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"border border-gray-200 rounded-lg p-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"font-medium text-gray-900 mb-2\",\n                    children: \"Workshop\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 136,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-600\",\n                    children: \"Ikuti workshop dan pelatihan yang tersedia\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 137,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 135,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"border border-gray-200 rounded-lg p-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"font-medium text-gray-900 mb-2\",\n                    children: \"Layanan Harian\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 140,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-600\",\n                    children: \"Akses layanan harian yang dapat membantu Anda\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 141,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 139,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"border border-gray-200 rounded-lg p-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"font-medium text-gray-900 mb-2\",\n                    children: \"Profil\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 144,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-600\",\n                    children: \"Kelola informasi profil dan pengaturan akun\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 145,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 143,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 130,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 16,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"CzcTeTziyjMsSrAVmHuCCb6+Bfg=\", false, function () {\n  return [useNavigate];\n});\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "FaUser", "FaSignOutAlt", "FaHome", "FaChartBar", "authService", "useNavigate", "jsxDEV", "_jsxDEV", "Dashboard", "_s", "_currentUser$role", "navigate", "currentUser", "getCurrentUser", "handleLogout", "logout", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "firstName", "username", "role", "onClick", "lastName", "char<PERSON>t", "toUpperCase", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/JOKI MAKALAH/halobantu1/halobantu/src/features/dashboard/Dashboard.js"], "sourcesContent": ["import React from 'react';\r\nimport { FaUser, FaSignOutAlt, FaHome, FaChartBar } from 'react-icons/fa';\r\nimport authService from '../../services/authService';\r\nimport { useNavigate } from 'react-router-dom';\r\n\r\nconst Dashboard = () => {\r\n  const navigate = useNavigate();\r\n  const currentUser = authService.getCurrentUser();\r\n\r\n  const handleLogout = async () => {\r\n    await authService.logout();\r\n    navigate('/login');\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gray-50\">\r\n      {/* Header */}\r\n      <header className=\"bg-white shadow-sm border-b\">\r\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n          <div className=\"flex justify-between items-center h-16\">\r\n            <div className=\"flex items-center\">\r\n              <FaHome className=\"h-8 w-8 text-blue-600 mr-3\" />\r\n              <h1 className=\"text-xl font-semibold text-gray-900\">HaloBantu - User Dashboard</h1>\r\n            </div>\r\n            <div className=\"flex items-center space-x-4\">\r\n              <div className=\"flex items-center text-sm text-gray-700\">\r\n                <FaUser className=\"h-4 w-4 mr-2\" />\r\n                <span>Halo, {currentUser?.firstName || currentUser?.username}!</span>\r\n                <span className=\"ml-2 px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full\">\r\n                  {currentUser?.role}\r\n                </span>\r\n              </div>\r\n              <button\r\n                onClick={handleLogout}\r\n                className=\"flex items-center text-sm text-red-600 hover:text-red-800 transition-colors\"\r\n              >\r\n                <FaSignOutAlt className=\"h-4 w-4 mr-1\" />\r\n                Logout\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </header>\r\n\r\n      {/* Main Content */}\r\n      <main className=\"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8\">\r\n        <div className=\"px-4 py-6 sm:px-0\">\r\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\r\n            {/* Welcome Card */}\r\n            <div className=\"bg-white overflow-hidden shadow rounded-lg\">\r\n              <div className=\"p-5\">\r\n                <div className=\"flex items-center\">\r\n                  <div className=\"flex-shrink-0\">\r\n                    <FaUser className=\"h-8 w-8 text-blue-600\" />\r\n                  </div>\r\n                  <div className=\"ml-5 w-0 flex-1\">\r\n                    <dl>\r\n                      <dt className=\"text-sm font-medium text-gray-500 truncate\">\r\n                        Selamat Datang\r\n                      </dt>\r\n                      <dd className=\"text-lg font-medium text-gray-900\">\r\n                        {currentUser?.firstName ?\r\n                          `${currentUser.firstName} ${currentUser.lastName}` :\r\n                          currentUser?.username\r\n                        }\r\n                      </dd>\r\n                    </dl>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Stats Card */}\r\n            <div className=\"bg-white overflow-hidden shadow rounded-lg\">\r\n              <div className=\"p-5\">\r\n                <div className=\"flex items-center\">\r\n                  <div className=\"flex-shrink-0\">\r\n                    <FaChartBar className=\"h-8 w-8 text-green-600\" />\r\n                  </div>\r\n                  <div className=\"ml-5 w-0 flex-1\">\r\n                    <dl>\r\n                      <dt className=\"text-sm font-medium text-gray-500 truncate\">\r\n                        Status Akun\r\n                      </dt>\r\n                      <dd className=\"text-lg font-medium text-gray-900\">\r\n                        Aktif\r\n                      </dd>\r\n                    </dl>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Role Card */}\r\n            <div className=\"bg-white overflow-hidden shadow rounded-lg\">\r\n              <div className=\"p-5\">\r\n                <div className=\"flex items-center\">\r\n                  <div className=\"flex-shrink-0\">\r\n                    <div className=\"h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center\">\r\n                      <span className=\"text-blue-600 font-semibold text-sm\">\r\n                        {currentUser?.role?.charAt(0).toUpperCase()}\r\n                      </span>\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"ml-5 w-0 flex-1\">\r\n                    <dl>\r\n                      <dt className=\"text-sm font-medium text-gray-500 truncate\">\r\n                        Role\r\n                      </dt>\r\n                      <dd className=\"text-lg font-medium text-gray-900 capitalize\">\r\n                        {currentUser?.role}\r\n                      </dd>\r\n                    </dl>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Content Area */}\r\n          <div className=\"mt-8\">\r\n            <div className=\"bg-white shadow rounded-lg\">\r\n              <div className=\"px-4 py-5 sm:p-6\">\r\n                <h3 className=\"text-lg leading-6 font-medium text-gray-900 mb-4\">\r\n                  Dashboard User\r\n                </h3>\r\n                <p className=\"text-gray-600 mb-4\">\r\n                  Selamat datang di dashboard user HaloBantu. Dari sini Anda dapat mengakses berbagai layanan yang tersedia.\r\n                </p>\r\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n                  <div className=\"border border-gray-200 rounded-lg p-4\">\r\n                    <h4 className=\"font-medium text-gray-900 mb-2\">Konsultasi Psikologi</h4>\r\n                    <p className=\"text-sm text-gray-600\">Akses layanan konsultasi dengan psikolog profesional</p>\r\n                  </div>\r\n                  <div className=\"border border-gray-200 rounded-lg p-4\">\r\n                    <h4 className=\"font-medium text-gray-900 mb-2\">Workshop</h4>\r\n                    <p className=\"text-sm text-gray-600\">Ikuti workshop dan pelatihan yang tersedia</p>\r\n                  </div>\r\n                  <div className=\"border border-gray-200 rounded-lg p-4\">\r\n                    <h4 className=\"font-medium text-gray-900 mb-2\">Layanan Harian</h4>\r\n                    <p className=\"text-sm text-gray-600\">Akses layanan harian yang dapat membantu Anda</p>\r\n                  </div>\r\n                  <div className=\"border border-gray-200 rounded-lg p-4\">\r\n                    <h4 className=\"font-medium text-gray-900 mb-2\">Profil</h4>\r\n                    <p className=\"text-sm text-gray-600\">Kelola informasi profil dan pengaturan akun</p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </main>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Dashboard;"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,EAAEC,YAAY,EAAEC,MAAM,EAAEC,UAAU,QAAQ,gBAAgB;AACzE,OAAOC,WAAW,MAAM,4BAA4B;AACpD,SAASC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,iBAAA;EACtB,MAAMC,QAAQ,GAAGN,WAAW,CAAC,CAAC;EAC9B,MAAMO,WAAW,GAAGR,WAAW,CAACS,cAAc,CAAC,CAAC;EAEhD,MAAMC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,MAAMV,WAAW,CAACW,MAAM,CAAC,CAAC;IAC1BJ,QAAQ,CAAC,QAAQ,CAAC;EACpB,CAAC;EAED,oBACEJ,OAAA;IAAKS,SAAS,EAAC,yBAAyB;IAAAC,QAAA,gBAEtCV,OAAA;MAAQS,SAAS,EAAC,6BAA6B;MAAAC,QAAA,eAC7CV,OAAA;QAAKS,SAAS,EAAC,wCAAwC;QAAAC,QAAA,eACrDV,OAAA;UAAKS,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrDV,OAAA;YAAKS,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCV,OAAA,CAACL,MAAM;cAACc,SAAS,EAAC;YAA4B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjDd,OAAA;cAAIS,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EAAC;YAA0B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChF,CAAC,eACNd,OAAA;YAAKS,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1CV,OAAA;cAAKS,SAAS,EAAC,yCAAyC;cAAAC,QAAA,gBACtDV,OAAA,CAACP,MAAM;gBAACgB,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACnCd,OAAA;gBAAAU,QAAA,GAAM,QAAM,EAAC,CAAAL,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEU,SAAS,MAAIV,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEW,QAAQ,GAAC,GAAC;cAAA;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACrEd,OAAA;gBAAMS,SAAS,EAAC,iEAAiE;gBAAAC,QAAA,EAC9EL,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEY;cAAI;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNd,OAAA;cACEkB,OAAO,EAAEX,YAAa;cACtBE,SAAS,EAAC,6EAA6E;cAAAC,QAAA,gBAEvFV,OAAA,CAACN,YAAY;gBAACe,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,UAE3C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGTd,OAAA;MAAMS,SAAS,EAAC,wCAAwC;MAAAC,QAAA,eACtDV,OAAA;QAAKS,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCV,OAAA;UAAKS,SAAS,EAAC,sDAAsD;UAAAC,QAAA,gBAEnEV,OAAA;YAAKS,SAAS,EAAC,4CAA4C;YAAAC,QAAA,eACzDV,OAAA;cAAKS,SAAS,EAAC,KAAK;cAAAC,QAAA,eAClBV,OAAA;gBAAKS,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChCV,OAAA;kBAAKS,SAAS,EAAC,eAAe;kBAAAC,QAAA,eAC5BV,OAAA,CAACP,MAAM;oBAACgB,SAAS,EAAC;kBAAuB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzC,CAAC,eACNd,OAAA;kBAAKS,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,eAC9BV,OAAA;oBAAAU,QAAA,gBACEV,OAAA;sBAAIS,SAAS,EAAC,4CAA4C;sBAAAC,QAAA,EAAC;oBAE3D;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACLd,OAAA;sBAAIS,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAC9CL,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAEU,SAAS,GACrB,GAAGV,WAAW,CAACU,SAAS,IAAIV,WAAW,CAACc,QAAQ,EAAE,GAClDd,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEW;oBAAQ;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAErB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNd,OAAA;YAAKS,SAAS,EAAC,4CAA4C;YAAAC,QAAA,eACzDV,OAAA;cAAKS,SAAS,EAAC,KAAK;cAAAC,QAAA,eAClBV,OAAA;gBAAKS,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChCV,OAAA;kBAAKS,SAAS,EAAC,eAAe;kBAAAC,QAAA,eAC5BV,OAAA,CAACJ,UAAU;oBAACa,SAAS,EAAC;kBAAwB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9C,CAAC,eACNd,OAAA;kBAAKS,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,eAC9BV,OAAA;oBAAAU,QAAA,gBACEV,OAAA;sBAAIS,SAAS,EAAC,4CAA4C;sBAAAC,QAAA,EAAC;oBAE3D;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACLd,OAAA;sBAAIS,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAElD;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNd,OAAA;YAAKS,SAAS,EAAC,4CAA4C;YAAAC,QAAA,eACzDV,OAAA;cAAKS,SAAS,EAAC,KAAK;cAAAC,QAAA,eAClBV,OAAA;gBAAKS,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChCV,OAAA;kBAAKS,SAAS,EAAC,eAAe;kBAAAC,QAAA,eAC5BV,OAAA;oBAAKS,SAAS,EAAC,mEAAmE;oBAAAC,QAAA,eAChFV,OAAA;sBAAMS,SAAS,EAAC,qCAAqC;sBAAAC,QAAA,EAClDL,WAAW,aAAXA,WAAW,wBAAAF,iBAAA,GAAXE,WAAW,CAAEY,IAAI,cAAAd,iBAAA,uBAAjBA,iBAAA,CAAmBiB,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;oBAAC;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNd,OAAA;kBAAKS,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,eAC9BV,OAAA;oBAAAU,QAAA,gBACEV,OAAA;sBAAIS,SAAS,EAAC,4CAA4C;sBAAAC,QAAA,EAAC;oBAE3D;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACLd,OAAA;sBAAIS,SAAS,EAAC,8CAA8C;sBAAAC,QAAA,EACzDL,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEY;oBAAI;sBAAAN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNd,OAAA;UAAKS,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnBV,OAAA;YAAKS,SAAS,EAAC,4BAA4B;YAAAC,QAAA,eACzCV,OAAA;cAAKS,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/BV,OAAA;gBAAIS,SAAS,EAAC,kDAAkD;gBAAAC,QAAA,EAAC;cAEjE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLd,OAAA;gBAAGS,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAC;cAElC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJd,OAAA;gBAAKS,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,gBACpDV,OAAA;kBAAKS,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,gBACpDV,OAAA;oBAAIS,SAAS,EAAC,gCAAgC;oBAAAC,QAAA,EAAC;kBAAoB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACxEd,OAAA;oBAAGS,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAAoD;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1F,CAAC,eACNd,OAAA;kBAAKS,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,gBACpDV,OAAA;oBAAIS,SAAS,EAAC,gCAAgC;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC5Dd,OAAA;oBAAGS,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAA0C;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChF,CAAC,eACNd,OAAA;kBAAKS,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,gBACpDV,OAAA;oBAAIS,SAAS,EAAC,gCAAgC;oBAAAC,QAAA,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAClEd,OAAA;oBAAGS,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAA6C;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnF,CAAC,eACNd,OAAA;kBAAKS,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,gBACpDV,OAAA;oBAAIS,SAAS,EAAC,gCAAgC;oBAAAC,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC1Dd,OAAA;oBAAGS,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAA2C;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACZ,EAAA,CArJID,SAAS;EAAA,QACIH,WAAW;AAAA;AAAAwB,EAAA,GADxBrB,SAAS;AAuJf,eAAeA,SAAS;AAAC,IAAAqB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}