{"ast": null, "code": "'use strict';\n\nmodule.exports = function (exec) {\n  try {\n    return !!exec();\n  } catch (error) {\n    return true;\n  }\n};", "map": {"version": 3, "names": ["module", "exports", "exec", "error"], "sources": ["C:/Users/<USER>/Documents/JOKI MAKALAH/halobantu1/halobantu/node_modules/core-js-pure/internals/fails.js"], "sourcesContent": ["'use strict';\nmodule.exports = function (exec) {\n  try {\n    return !!exec();\n  } catch (error) {\n    return true;\n  }\n};\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,OAAO,GAAG,UAAUC,IAAI,EAAE;EAC/B,IAAI;IACF,OAAO,CAAC,CAACA,IAAI,CAAC,CAAC;EACjB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACd,OAAO,IAAI;EACb;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}