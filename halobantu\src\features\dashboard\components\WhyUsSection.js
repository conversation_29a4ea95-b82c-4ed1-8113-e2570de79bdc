import React from 'react';
import { FaShieldAlt, FaRocket, FaHeart, FaStar, FaHandshake, FaCog } from 'react-icons/fa';

const WhyUsSection = () => {
  const reasons = [
    {
      icon: <FaShieldAlt className="text-4xl text-blue-600" />,
      title: "Teknologi Terdepan",
      description: "Menggunakan teknologi canggih dan inovasi terbaru untuk memberikan solusi otomotif masa depan."
    },
    {
      icon: <FaRocket className="text-4xl text-purple-600" />,
      title: "Performa Optimal",
      description: "Layanan berkualitas tinggi dengan standar internasional untuk hasil yang maksimal dan memuaskan."
    },
    {
      icon: <FaHeart className="text-4xl text-red-600" />,
      title: "Pelayanan Premium",
      description: "Tim profesional berpengalaman siap memberikan layanan terbaik dengan pendekatan personal."
    },
    {
      icon: <FaStar className="text-4xl text-yellow-600" />,
      title: "Standar Internasional",
      description: "Mengikuti standar kualitas internasional dengan sertifikasi dan jaminan kualitas terbaik."
    },
    {
      icon: <FaHandshake className="text-4xl text-green-600" />,
      title: "Kepercayaan Tinggi",
      description: "Dipercaya oleh ribuan pelanggan dengan track record yang terbukti dan testimoni positif."
    },
    {
      icon: <FaCog className="text-4xl text-gray-600" />,
      title: "Inovasi Berkelanjutan",
      description: "Terus berinovasi dan mengembangkan teknologi untuk memberikan solusi terbaik di masa depan."
    }
  ];

  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-4">
            Kenapa Kami?
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Future X menghadirkan revolusi teknologi otomotif dengan standar kualitas internasional dan inovasi berkelanjutan
          </p>
        </div>

        {/* Reasons Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
          {reasons.map((reason, index) => (
            <div
              key={index}
              className="bg-white rounded-xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 group"
            >
              <div className="text-center">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-gray-50 rounded-full mb-6 group-hover:scale-110 transition-transform duration-300">
                  {reason.icon}
                </div>
                <h3 className="text-xl font-bold text-gray-800 mb-4">
                  {reason.title}
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  {reason.description}
                </p>
              </div>
            </div>
          ))}
        </div>

        {/* CTA Section */}
        <div className="text-center mt-16">
          <div className="bg-white rounded-2xl p-8 shadow-lg max-w-4xl mx-auto">
            <h3 className="text-3xl font-bold text-gray-800 mb-4">
              Siap Memasuki Era Future X?
            </h3>
            <p className="text-lg text-gray-600 mb-8">
              Bergabunglah dengan revolusi teknologi otomotif dan rasakan pengalaman masa depan hari ini
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button 
                onClick={() => document.getElementById('services').scrollIntoView({ behavior: 'smooth' })}
                className="px-8 py-4 bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-lg transition-all duration-300 transform hover:scale-105"
              >
                Mulai Sekarang
              </button>
              <button 
                onClick={() => document.getElementById('contact').scrollIntoView({ behavior: 'smooth' })}
                className="px-8 py-4 bg-gray-100 hover:bg-gray-200 text-gray-800 font-semibold rounded-lg transition-all duration-300"
              >
                Hubungi Kami
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default WhyUsSection;
