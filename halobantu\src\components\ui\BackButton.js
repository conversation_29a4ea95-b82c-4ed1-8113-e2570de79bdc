import React from 'react';
import { useNavigate } from 'react-router-dom';
import { FaArrowLeft } from 'react-icons/fa';

const BackButton = ({ targetPath = '/dashboard', label = 'Kembali ke Dashboard' }) => {
  const navigate = useNavigate();

  return (
    <div className="fixed top-4 left-4 z-50 bg-red-500 p-3 rounded-lg shadow-xl border-4 border-red-800 animate-pulse">
      <button
        onClick={() => navigate(targetPath)}
        className="flex items-center text-white text-lg font-bold hover:text-gray-200"
      >
        <FaArrowLeft className="mr-2" />
        {label} (CEK INI)
      </button>
    </div>
  );
};

export default BackButton; 