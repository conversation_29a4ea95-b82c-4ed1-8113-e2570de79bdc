const mysql = require('mysql2/promise');
require('dotenv').config();

const createDatabase = async () => {
  let connection;
  
  try {
    console.log('🔄 Connecting to MySQL server...');
    
    // Connect to MySQL server (without specifying database)
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || ''
    });
    
    console.log('✅ Connected to MySQL server');
    
    // Create database if it doesn't exist
    const dbName = process.env.DB_NAME || 'halobantu_db';
    await connection.execute(`CREATE DATABASE IF NOT EXISTS \`${dbName}\``);
    console.log(`✅ Database '${dbName}' created or already exists`);
    
    // Create user if specified and different from root
    if (process.env.DB_USER && process.env.DB_USER !== 'root') {
      try {
        await connection.execute(
          `CREATE USER IF NOT EXISTS '${process.env.DB_USER}'@'localhost' IDENTIFIED BY '${process.env.DB_PASSWORD}'`
        );
        console.log(`✅ User '${process.env.DB_USER}' created or already exists`);
        
        await connection.execute(
          `GRANT ALL PRIVILEGES ON \`${dbName}\`.* TO '${process.env.DB_USER}'@'localhost'`
        );
        console.log(`✅ Privileges granted to '${process.env.DB_USER}'`);
        
        await connection.execute('FLUSH PRIVILEGES');
        console.log('✅ Privileges flushed');
      } catch (userError) {
        console.log('⚠️  User creation skipped (may already exist or insufficient privileges)');
      }
    }
    
    console.log('🎉 Database setup completed successfully!');
    
  } catch (error) {
    console.error('❌ Error setting up database:', error.message);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 MySQL connection closed');
    }
  }
};

// Run if called directly
if (require.main === module) {
  createDatabase();
}

module.exports = createDatabase;
