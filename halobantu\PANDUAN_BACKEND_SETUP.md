# 🚀 Panduan Setup Backend Node.js untuk HaloBantu

## 📋 Langkah-Langkah Pembuatan Backend

### 1. 🗄️ **Setup Database**

#### **Pilihan Database:**
- **MySQL** (Recommended untuk production)
- **PostgreSQL** (Alternative yang powerful)
- **MongoDB** (NoSQL option)

#### **Langkah Database MySQL:**
```bash
# Install MySQL di sistem Anda
# Buat database baru
CREATE DATABASE halobantu_db;

# Buat user khusus (optional)
CREATE USER 'halobantu_user'@'localhost' IDENTIFIED BY 'password123';
GRANT ALL PRIVILEGES ON halobantu_db.* TO 'halobantu_user'@'localhost';
```

---

### 2. 📁 **Setup Struktur Backend**

#### **Buat folder backend:**
```bash
# Di root project HaloBantu
mkdir backend
cd backend
```

#### **Inisialisasi Node.js project:**
```bash
npm init -y
```

#### **Install Dependencies Utama:**
```bash
# Dependencies utama
npm install express cors dotenv bcryptjs jsonwebtoken mysql2 sequelize

# Development dependencies
npm install --save-dev nodemon concurrently
```

---

### 3. 📂 **Struktur Folder Backend**

```
backend/
├── config/
│   ├── database.js
│   └── auth.js
├── controllers/
│   ├── authController.js
│   └── userController.js
├── middleware/
│   ├── auth.js
│   └── validation.js
├── models/
│   ├── User.js
│   └── index.js
├── routes/
│   ├── auth.js
│   └── users.js
├── utils/
│   ├── helpers.js
│   └── validators.js
├── .env
├── .gitignore
├── package.json
└── server.js
```

---

### 4. ⚙️ **Konfigurasi Environment (.env)**

```env
# Database
DB_HOST=localhost
DB_USER=halobantu_user
DB_PASSWORD=password123
DB_NAME=halobantu_db
DB_PORT=3306

# JWT
JWT_SECRET=your_super_secret_jwt_key_here
JWT_EXPIRE=7d

# Server
PORT=5000
NODE_ENV=development

# CORS
FRONTEND_URL=http://localhost:3000
```

---

### 5. 🗃️ **Setup Database Models**

#### **User Model (models/User.js):**
```javascript
// Struktur tabel users
const userSchema = {
  id: 'PRIMARY KEY AUTO_INCREMENT',
  email: 'VARCHAR(255) UNIQUE NOT NULL',
  password: 'VARCHAR(255) NOT NULL',
  firstName: 'VARCHAR(100) NOT NULL',
  lastName: 'VARCHAR(100) NOT NULL',
  phone: 'VARCHAR(20)',
  role: 'ENUM("user", "admin", "psychologist") DEFAULT "user"',
  isActive: 'BOOLEAN DEFAULT true',
  emailVerified: 'BOOLEAN DEFAULT false',
  createdAt: 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
  updatedAt: 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
}
```

---

### 6. 🔐 **Setup Authentication System**

#### **Fitur yang akan dibuat:**
- ✅ Register user baru
- ✅ Login dengan email/password
- ✅ JWT token generation
- ✅ Password hashing (bcrypt)
- ✅ Email verification (optional)
- ✅ Forgot password
- ✅ Protected routes middleware
- ✅ Role-based access control

---

### 7. 🛣️ **API Routes yang akan dibuat**

#### **Auth Routes (/api/auth):**
```
POST /api/auth/register     - Daftar user baru
POST /api/auth/login        - Login user
POST /api/auth/logout       - Logout user
POST /api/auth/refresh      - Refresh JWT token
POST /api/auth/forgot       - Forgot password
POST /api/auth/reset        - Reset password
GET  /api/auth/verify/:token - Verify email
```

#### **User Routes (/api/users):**
```
GET    /api/users/profile   - Get user profile
PUT    /api/users/profile   - Update user profile
DELETE /api/users/account   - Delete user account
GET    /api/users/          - Get all users (admin only)
```

---

### 8. 📝 **File Utama yang Perlu Dibuat**

#### **server.js** - Entry point
#### **config/database.js** - Database connection
#### **config/auth.js** - JWT configuration
#### **controllers/authController.js** - Auth logic
#### **middleware/auth.js** - JWT verification
#### **routes/auth.js** - Auth endpoints
#### **models/User.js** - User model

---

### 9. 🔧 **Package.json Scripts**

```json
{
  "scripts": {
    "start": "node server.js",
    "dev": "nodemon server.js",
    "dev:full": "concurrently \"npm run dev\" \"cd ../frontend && npm start\"",
    "migrate": "node scripts/migrate.js",
    "seed": "node scripts/seed.js"
  }
}
```

---

### 10. 🚀 **Langkah Implementasi**

#### **Urutan Pengerjaan:**
1. ✅ Setup project structure
2. ✅ Install dependencies
3. ✅ Configure environment variables
4. ✅ Setup database connection
5. ✅ Create User model
6. ✅ Create database tables
7. ✅ Implement auth controller
8. ✅ Create auth middleware
9. ✅ Setup routes
10. ✅ Test API endpoints
11. ✅ Connect with frontend
12. ✅ Add validation & error handling

---

### 11. 🧪 **Testing API**

#### **Tools untuk testing:**
- **Postman** - GUI testing
- **Thunder Client** (VS Code extension)
- **curl** - Command line testing

#### **Test endpoints:**
```bash
# Register
POST http://localhost:5000/api/auth/register

# Login
POST http://localhost:5000/api/auth/login

# Protected route
GET http://localhost:5000/api/users/profile
Authorization: Bearer <jwt_token>
```

---

### 12. 🔒 **Security Features**

#### **Yang akan diimplementasi:**
- ✅ Password hashing dengan bcrypt
- ✅ JWT token authentication
- ✅ CORS configuration
- ✅ Rate limiting
- ✅ Input validation & sanitization
- ✅ SQL injection prevention
- ✅ XSS protection
- ✅ Environment variables untuk secrets

---

### 13. 📱 **Integrasi dengan Frontend**

#### **Frontend changes needed:**
- ✅ Update API base URL
- ✅ Create auth service functions
- ✅ Add token storage (localStorage/cookies)
- ✅ Create auth context
- ✅ Add protected route components
- ✅ Update login/register forms

---

### 14. 🚀 **Deployment Preparation**

#### **Production considerations:**
- ✅ Environment-specific configs
- ✅ Database migrations
- ✅ Error logging
- ✅ API documentation
- ✅ Health check endpoints
- ✅ Docker configuration (optional)

---

## 🎯 **Next Steps**

Setelah setup ini selesai, Anda akan memiliki:
- ✅ Backend API yang lengkap untuk auth
- ✅ Database yang terstruktur
- ✅ Security yang proper
- ✅ Integration-ready dengan frontend React

**Apakah Anda siap untuk mulai implementasi? Saya bisa membantu membuat file-file yang diperlukan satu per satu!**
