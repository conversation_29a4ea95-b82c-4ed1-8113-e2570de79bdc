import React from 'react';
import { Navigate } from 'react-router-dom';
import authService from '../../services/authService';

const ProtectedRoute = ({ children, requiredRole = null }) => {
  const isAuthenticated = authService.isAuthenticated();
  const currentUser = authService.getCurrentUser();

  console.log('🔒 ProtectedRoute check:', {
    isAuthenticated,
    currentUser,
    requiredRole
  });

  // Jika tidak login, redirect ke login
  if (!isAuthenticated) {
    console.log('❌ Not authenticated, redirecting to login');
    return <Navigate to="/login" replace />;
  }

  // Jika ada role requirement dan user tidak memiliki role yang sesuai
  if (requiredRole && currentUser?.role !== requiredRole) {
    console.log(`❌ Role mismatch. Required: ${requiredRole}, User: ${currentUser?.role}`);
    // Redirect berdasarkan role user
    if (currentUser?.role === 'admin') {
      console.log('🔄 Redirecting admin to /admin');
      return <Navigate to="/admin" replace />;
    } else {
      console.log('🔄 Redirecting user to /dashboard');
      return <Navigate to="/dashboard" replace />;
    }
  }

  console.log('✅ Access granted');
  return children;
};

export default ProtectedRoute;
