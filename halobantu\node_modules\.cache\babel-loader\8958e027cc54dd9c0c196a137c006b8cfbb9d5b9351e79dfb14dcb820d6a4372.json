{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\JOKI MAKALAH\\\\halobantu1\\\\halobantu\\\\src\\\\features\\\\dashboard\\\\components\\\\ServicesSection.js\";\nimport React from 'react';\nimport { FaTools, FaUserMd, FaHandsHelping } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ServicesSection = () => {\n  const services = [{\n    title: \"Bengkel\",\n    description: \"Layanan perbaikan dan perawatan kendaraan dengan teknologi canggih dan teknisi berpengalaman untuk performa optimal kendaraan masa depan.\",\n    icon: /*#__PURE__*/_jsxDEV(FaTools, {\n      className: \"text-4xl\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 13\n    }, this),\n    color: \"from-blue-500 to-blue-600\",\n    path: \"/workshop\",\n    image: \"https://images.pexels.com/photos/3806288/pexels-photo-3806288.jpeg?auto=compress&cs=tinysrgb&w=400&h=300&fit=crop\"\n  }, {\n    title: \"Konsultasi Psikologi\",\n    description: \"Konsultasi profesional dengan psikolog berpengalaman untuk kesehatan mental dan pengembangan diri yang optimal di era digital.\",\n    icon: /*#__PURE__*/_jsxDEV(FaUserMd, {\n      className: \"text-4xl\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 13\n    }, this),\n    color: \"from-purple-500 to-purple-600\",\n    path: \"/psychology\",\n    image: \"https://images.pexels.com/photos/7176026/pexels-photo-7176026.jpeg?auto=compress&cs=tinysrgb&w=400&h=300&fit=crop\"\n  }, {\n    title: \"Opo Wae\",\n    description: \"Solusi lengkap untuk berbagai kebutuhan sehari-hari dengan layanan berkualitas tinggi dan teknologi terdepan untuk kemudahan hidup.\",\n    icon: /*#__PURE__*/_jsxDEV(FaHandsHelping, {\n      className: \"text-4xl\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 13\n    }, this),\n    color: \"from-green-500 to-green-600\",\n    path: \"/daily-services\",\n    image: \"https://images.pexels.com/photos/4792509/pexels-photo-4792509.jpeg?auto=compress&cs=tinysrgb&w=400&h=300&fit=crop\"\n  }];\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    id: \"services\",\n    className: \"py-20 bg-gray-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-16\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-4xl md:text-5xl font-bold text-gray-800 mb-4\",\n          children: \"Layanan Kami\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n          children: \"Solusi teknologi terdepan untuk masa depan yang lebih baik dengan layanan berkualitas premium\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto\",\n        children: services.map((service, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"group bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative h-48 overflow-hidden\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: service.image,\n              alt: service.title,\n              className: \"w-full h-full object-cover group-hover:scale-110 transition-transform duration-300\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 54,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `absolute inset-0 bg-gradient-to-t ${service.color} opacity-80`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 59,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-0 flex items-center justify-center text-white\",\n              children: service.icon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 60,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-2xl font-bold text-gray-800 mb-3\",\n              children: service.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 67,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 mb-6 leading-relaxed\",\n              children: service.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 70,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => window.location.href = service.path,\n              className: `w-full py-3 px-6 bg-gradient-to-r ${service.color} text-white font-semibold rounded-lg hover:shadow-lg transition-all duration-300 transform hover:scale-105`,\n              children: \"Lihat Layanan\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 73,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 15\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 33,\n    columnNumber: 5\n  }, this);\n};\n_c = ServicesSection;\nexport default ServicesSection;\nvar _c;\n$RefreshReg$(_c, \"ServicesSection\");", "map": {"version": 3, "names": ["React", "FaTools", "FaUserMd", "FaHandsHelping", "jsxDEV", "_jsxDEV", "ServicesSection", "services", "title", "description", "icon", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "path", "image", "id", "children", "map", "service", "index", "src", "alt", "onClick", "window", "location", "href", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/JOKI MAKALAH/halobantu1/halobantu/src/features/dashboard/components/ServicesSection.js"], "sourcesContent": ["import React from 'react';\nimport { FaTools, FaUserMd, FaHandsHelping } from 'react-icons/fa';\n\nconst ServicesSection = () => {\n  const services = [\n    {\n      title: \"Bengkel\",\n      description: \"Layanan perbaikan dan perawatan kendaraan dengan teknologi canggih dan teknisi berpengalaman untuk performa optimal kendaraan masa depan.\",\n      icon: <FaTools className=\"text-4xl\" />,\n      color: \"from-blue-500 to-blue-600\",\n      path: \"/workshop\",\n      image: \"https://images.pexels.com/photos/3806288/pexels-photo-3806288.jpeg?auto=compress&cs=tinysrgb&w=400&h=300&fit=crop\"\n    },\n    {\n      title: \"Konsultasi Psikologi\",\n      description: \"Konsultasi profesional dengan psikolog berpengalaman untuk kesehatan mental dan pengembangan diri yang optimal di era digital.\",\n      icon: <FaUserMd className=\"text-4xl\" />,\n      color: \"from-purple-500 to-purple-600\",\n      path: \"/psychology\",\n      image: \"https://images.pexels.com/photos/7176026/pexels-photo-7176026.jpeg?auto=compress&cs=tinysrgb&w=400&h=300&fit=crop\"\n    },\n    {\n      title: \"Opo Wae\",\n      description: \"Solusi lengkap untuk berbagai kebutuhan sehari-hari dengan layanan berkualitas tinggi dan teknologi terdepan untuk kemudahan hidup.\",\n      icon: <FaHandsHelping className=\"text-4xl\" />,\n      color: \"from-green-500 to-green-600\",\n      path: \"/daily-services\",\n      image: \"https://images.pexels.com/photos/4792509/pexels-photo-4792509.jpeg?auto=compress&cs=tinysrgb&w=400&h=300&fit=crop\"\n    }\n  ];\n\n  return (\n    <section id=\"services\" className=\"py-20 bg-gray-50\">\n      <div className=\"container mx-auto px-4\">\n        {/* Section Header */}\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-4xl md:text-5xl font-bold text-gray-800 mb-4\">\n            Layanan Kami\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            Solusi teknologi terdepan untuk masa depan yang lebih baik dengan layanan berkualitas premium\n          </p>\n        </div>\n\n        {/* Services Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto\">\n          {services.map((service, index) => (\n            <div\n              key={index}\n              className=\"group bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2\"\n            >\n              {/* Service Image */}\n              <div className=\"relative h-48 overflow-hidden\">\n                <img\n                  src={service.image}\n                  alt={service.title}\n                  className=\"w-full h-full object-cover group-hover:scale-110 transition-transform duration-300\"\n                />\n                <div className={`absolute inset-0 bg-gradient-to-t ${service.color} opacity-80`}></div>\n                <div className=\"absolute inset-0 flex items-center justify-center text-white\">\n                  {service.icon}\n                </div>\n              </div>\n\n              {/* Service Content */}\n              <div className=\"p-6\">\n                <h3 className=\"text-2xl font-bold text-gray-800 mb-3\">\n                  {service.title}\n                </h3>\n                <p className=\"text-gray-600 mb-6 leading-relaxed\">\n                  {service.description}\n                </p>\n                <button\n                  onClick={() => window.location.href = service.path}\n                  className={`w-full py-3 px-6 bg-gradient-to-r ${service.color} text-white font-semibold rounded-lg hover:shadow-lg transition-all duration-300 transform hover:scale-105`}\n                >\n                  Lihat Layanan\n                </button>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default ServicesSection;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,EAAEC,QAAQ,EAAEC,cAAc,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnE,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAC5B,MAAMC,QAAQ,GAAG,CACf;IACEC,KAAK,EAAE,SAAS;IAChBC,WAAW,EAAE,2IAA2I;IACxJC,IAAI,eAAEL,OAAA,CAACJ,OAAO;MAACU,SAAS,EAAC;IAAU;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtCC,KAAK,EAAE,2BAA2B;IAClCC,IAAI,EAAE,WAAW;IACjBC,KAAK,EAAE;EACT,CAAC,EACD;IACEV,KAAK,EAAE,sBAAsB;IAC7BC,WAAW,EAAE,gIAAgI;IAC7IC,IAAI,eAAEL,OAAA,CAACH,QAAQ;MAACS,SAAS,EAAC;IAAU;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvCC,KAAK,EAAE,+BAA+B;IACtCC,IAAI,EAAE,aAAa;IACnBC,KAAK,EAAE;EACT,CAAC,EACD;IACEV,KAAK,EAAE,SAAS;IAChBC,WAAW,EAAE,qIAAqI;IAClJC,IAAI,eAAEL,OAAA,CAACF,cAAc;MAACQ,SAAS,EAAC;IAAU;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC7CC,KAAK,EAAE,6BAA6B;IACpCC,IAAI,EAAE,iBAAiB;IACvBC,KAAK,EAAE;EACT,CAAC,CACF;EAED,oBACEb,OAAA;IAASc,EAAE,EAAC,UAAU;IAACR,SAAS,EAAC,kBAAkB;IAAAS,QAAA,eACjDf,OAAA;MAAKM,SAAS,EAAC,wBAAwB;MAAAS,QAAA,gBAErCf,OAAA;QAAKM,SAAS,EAAC,mBAAmB;QAAAS,QAAA,gBAChCf,OAAA;UAAIM,SAAS,EAAC,mDAAmD;UAAAS,QAAA,EAAC;QAElE;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLV,OAAA;UAAGM,SAAS,EAAC,yCAAyC;UAAAS,QAAA,EAAC;QAEvD;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGNV,OAAA;QAAKM,SAAS,EAAC,wEAAwE;QAAAS,QAAA,EACpFb,QAAQ,CAACc,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBAC3BlB,OAAA;UAEEM,SAAS,EAAC,kIAAkI;UAAAS,QAAA,gBAG5If,OAAA;YAAKM,SAAS,EAAC,+BAA+B;YAAAS,QAAA,gBAC5Cf,OAAA;cACEmB,GAAG,EAAEF,OAAO,CAACJ,KAAM;cACnBO,GAAG,EAAEH,OAAO,CAACd,KAAM;cACnBG,SAAS,EAAC;YAAoF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/F,CAAC,eACFV,OAAA;cAAKM,SAAS,EAAE,qCAAqCW,OAAO,CAACN,KAAK;YAAc;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvFV,OAAA;cAAKM,SAAS,EAAC,8DAA8D;cAAAS,QAAA,EAC1EE,OAAO,CAACZ;YAAI;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNV,OAAA;YAAKM,SAAS,EAAC,KAAK;YAAAS,QAAA,gBAClBf,OAAA;cAAIM,SAAS,EAAC,uCAAuC;cAAAS,QAAA,EAClDE,OAAO,CAACd;YAAK;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,eACLV,OAAA;cAAGM,SAAS,EAAC,oCAAoC;cAAAS,QAAA,EAC9CE,OAAO,CAACb;YAAW;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC,eACJV,OAAA;cACEqB,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAGP,OAAO,CAACL,IAAK;cACnDN,SAAS,EAAE,qCAAqCW,OAAO,CAACN,KAAK,4GAA6G;cAAAI,QAAA,EAC3K;YAED;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA,GA9BDQ,KAAK;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA+BP,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAACe,EAAA,GAlFIxB,eAAe;AAoFrB,eAAeA,eAAe;AAAC,IAAAwB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}