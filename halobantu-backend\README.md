# 🚀 HaloBantu Backend API

Backend API untuk aplikasi HaloBantu - Platform Konsultasi Psikologi yang dibangun dengan Node.js, Express, dan MySQL.

## 📋 Fitur Utama

- ✅ **Autentikasi & Otorisasi** dengan JWT
- ✅ **Registrasi & Login** pengguna
- ✅ **Manajemen Profil** pengguna
- ✅ **Role-based Access Control** (<PERSON><PERSON>, <PERSON><PERSON>, Psychologist)
- ✅ **Password Reset** via email
- ✅ **Rate Limiting** untuk keamanan
- ✅ **Input Validation** & sanitization
- ✅ **Error Handling** yang komprehensif

## 🛠️ Tech Stack

- **Runtime**: Node.js
- **Framework**: Express.js
- **Database**: MySQL
- **ORM**: Sequelize
- **Authentication**: JWT (JSON Web Tokens)
- **Password Hashing**: bcryptjs
- **Validation**: express-validator
- **Security**: helmet, cors, rate-limiting

## 📁 Struktur Proyek

```
halobantu-backend/
├── config/
│   ├── database.js      # Konfigurasi database
│   └── auth.js          # Konfigurasi autentikasi
├── controllers/
│   ├── authController.js    # Logic autentikasi
│   └── userController.js    # Logic manajemen user
├── middleware/
│   ├── auth.js          # Middleware autentikasi
│   └── validation.js    # Middleware validasi
├── models/
│   ├── User.js          # Model pengguna
│   └── index.js         # Export semua model
├── routes/
│   ├── auth.js          # Routes autentikasi
│   └── users.js         # Routes pengguna
├── scripts/
│   ├── createDatabase.js    # Script buat database
│   └── syncDatabase.js      # Script sinkronisasi model
├── utils/
│   └── helpers.js       # Utility functions
├── .env                 # Environment variables
├── .gitignore          # Git ignore rules
├── package.json        # Dependencies & scripts
└── server.js           # Entry point aplikasi
```

## 🚀 Quick Start

### 1. Prerequisites

- Node.js (v16 atau lebih baru)
- MySQL Server
- phpMyAdmin (opsional)

### 2. Installation

```bash
# Clone atau download backend
cd halobantu-backend

# Install dependencies
npm install

# Install nodemon untuk development
npm install --save-dev nodemon
```

### 3. Environment Setup

Buat file `.env` dan sesuaikan konfigurasi:

```env
# Database
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=
DB_NAME=halobantu_db
DB_PORT=3306

# JWT
JWT_SECRET=your_super_secret_jwt_key_here
JWT_EXPIRE=7d

# Server
PORT=5000
NODE_ENV=development

# CORS
FRONTEND_URL=http://localhost:3000
```

### 4. Database Setup

```bash
# Buat database dan tabel
npm run setup

# Atau jalankan step by step:
npm run db:create    # Buat database
npm run db:sync      # Sinkronisasi model
```

### 5. Jalankan Server

```bash
# Development mode (dengan auto-reload)
npm run dev

# Production mode
npm start
```

Server akan berjalan di `http://localhost:5000`

## 📚 API Endpoints

### 🔐 Authentication Routes (`/api/auth`)

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| POST | `/register` | Daftar pengguna baru | ❌ |
| POST | `/login` | Login pengguna | ❌ |
| POST | `/refresh` | Refresh JWT token | ❌ |
| POST | `/forgot-password` | Kirim reset password | ❌ |
| POST | `/reset-password` | Reset password | ❌ |
| GET | `/profile` | Get profil pengguna | ✅ |
| POST | `/logout` | Logout pengguna | ✅ |

### 👥 User Routes (`/api/users`)

| Method | Endpoint | Description | Auth Required | Role |
|--------|----------|-------------|---------------|------|
| GET | `/profile` | Get profil sendiri | ✅ | Any |
| PUT | `/profile` | Update profil | ✅ | Any |
| PUT | `/change-password` | Ganti password | ✅ | Any |
| DELETE | `/deactivate` | Nonaktifkan akun | ✅ | Any |
| GET | `/` | Get semua pengguna | ✅ | Admin |
| GET | `/:id` | Get pengguna by ID | ✅ | Owner/Admin |
| PUT | `/:id/role` | Update role pengguna | ✅ | Admin |
| PUT | `/:id/toggle-status` | Toggle status aktif | ✅ | Admin |

## 🧪 Testing API

### Menggunakan curl:

```bash
# Register
curl -X POST http://localhost:5000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "Password123",
    "confirmPassword": "Password123",
    "firstName": "John",
    "lastName": "Doe"
  }'

# Login
curl -X POST http://localhost:5000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "Password123"
  }'

# Get Profile (dengan token)
curl -X GET http://localhost:5000/api/auth/profile \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Default Admin Account

Setelah setup database, akun admin default akan dibuat:
- **Email**: `<EMAIL>`
- **Password**: `Admin123!`

⚠️ **Penting**: Ganti password setelah login pertama!

## 🔒 Security Features

- **JWT Authentication** dengan refresh token
- **Password Hashing** menggunakan bcrypt
- **Rate Limiting** untuk mencegah spam
- **Input Validation** & sanitization
- **CORS Protection**
- **Helmet** untuk security headers
- **Account Lockout** setelah failed login attempts

## 🐛 Troubleshooting

### Database Connection Error
```bash
# Pastikan MySQL berjalan
# Periksa kredensial di .env
# Jalankan script database
npm run db:create
```

### Port Already in Use
```bash
# Ganti PORT di .env atau kill process
lsof -ti:5000 | xargs kill -9  # macOS/Linux
netstat -ano | findstr :5000   # Windows
```

### JWT Token Error
```bash
# Pastikan JWT_SECRET di .env
# Periksa format Authorization header: "Bearer <token>"
```

## 📝 Development Notes

- Gunakan `npm run dev` untuk development dengan auto-reload
- Database akan otomatis sync saat server start
- Semua password di-hash dengan bcrypt
- Token JWT expire dalam 7 hari (configurable)
- Rate limit: 100 requests per 15 menit per IP

## 🚀 Deployment

1. Set `NODE_ENV=production` di environment
2. Gunakan database production
3. Set JWT_SECRET yang kuat
4. Enable HTTPS
5. Configure reverse proxy (nginx)
6. Set up monitoring & logging

## 📞 Support

Jika ada pertanyaan atau masalah, silakan buat issue atau hubungi tim development.

---

**Happy Coding! 🎉**
