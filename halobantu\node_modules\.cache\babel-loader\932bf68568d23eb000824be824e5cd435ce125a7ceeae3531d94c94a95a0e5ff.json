{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\JOKI MAKALAH\\\\halobantu1\\\\halobantu\\\\src\\\\features\\\\admin\\\\AdminDashboard.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Link, useLocation, useNavigate } from 'react-router-dom';\nimport { FaUsers, FaCog, FaChartBar, FaSignOutAlt, FaUserShield } from 'react-icons/fa';\nimport authService from '../../services/authService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminDashboard = () => {\n  _s();\n  const location = useLocation();\n  const navigate = useNavigate();\n  const currentUser = authService.getCurrentUser();\n  const navLinks = [{\n    path: '/admin/users',\n    label: '<PERSON><PERSON><PERSON>',\n    icon: FaUsers\n  }, {\n    path: '/admin/settings',\n    label: 'Pengaturan',\n    icon: FaCog\n  }, {\n    path: '/admin/reports',\n    label: 'Laporan',\n    icon: FaChartBar\n  }];\n  const handleLogout = async () => {\n    await authService.logout();\n    navigate('/login');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"w-64 bg-gray-800 text-white flex flex-col shadow-lg\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-6 border-b border-gray-700\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(FaUserShield, {\n            className: \"h-8 w-8 text-blue-300 mr-3\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 28,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-xl font-bold text-blue-300\",\n              children: \"HaloBantu\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 30,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-300\",\n              children: \"Admin Panel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 31,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 29,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n        className: \"flex-1 px-4 py-8 space-y-3\",\n        children: navLinks.map(link => {\n          const IconComponent = link.icon;\n          return /*#__PURE__*/_jsxDEV(Link, {\n            to: link.path,\n            className: `flex items-center px-5 py-3 rounded-lg text-lg font-medium transition-all duration-300\n                  ${location.pathname === link.path ? 'bg-blue-600 text-white shadow-md' : 'hover:bg-gray-700 hover:text-blue-200'}`,\n            children: [/*#__PURE__*/_jsxDEV(IconComponent, {\n              className: \"h-5 w-5 mr-3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 48,\n              columnNumber: 17\n            }, this), link.label]\n          }, link.path, true, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 15\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 flex flex-col\",\n      children: [/*#__PURE__*/_jsxDEV(\"header\", {\n        className: \"bg-white shadow-md p-6 border-b border-gray-200 flex justify-between items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold text-gray-800\",\n          children: \"Admin Dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center text-sm text-gray-700\",\n            children: [/*#__PURE__*/_jsxDEV(FaUserShield, {\n              className: \"h-4 w-4 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 62,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [\"Halo, \", (currentUser === null || currentUser === void 0 ? void 0 : currentUser.firstName) || (currentUser === null || currentUser === void 0 ? void 0 : currentUser.username), \"!\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 63,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"ml-2 px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full\",\n              children: currentUser === null || currentUser === void 0 ? void 0 : currentUser.role\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 64,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 61,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleLogout,\n            className: \"flex items-center px-4 py-2 text-sm text-red-600 bg-red-100 rounded-md hover:bg-red-200 transition-colors font-semibold\",\n            children: [/*#__PURE__*/_jsxDEV(FaSignOutAlt, {\n              className: \"h-4 w-4 mr-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 72,\n              columnNumber: 15\n            }, this), \"Logout\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: \"flex-1 p-10\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow p-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-3 rounded-full bg-blue-100\",\n                children: /*#__PURE__*/_jsxDEV(FaUsers, {\n                  className: \"h-6 w-6 text-blue-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 83,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 82,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"ml-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-600\",\n                  children: \"Total Users\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 86,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-2xl font-semibold text-gray-900\",\n                  children: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 87,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 85,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 81,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow p-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-3 rounded-full bg-green-100\",\n                children: /*#__PURE__*/_jsxDEV(FaChartBar, {\n                  className: \"h-6 w-6 text-green-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 95,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 94,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"ml-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-600\",\n                  children: \"Active Sessions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 98,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-2xl font-semibold text-gray-900\",\n                  children: \"1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 99,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 97,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 93,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow p-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-3 rounded-full bg-purple-100\",\n                children: /*#__PURE__*/_jsxDEV(FaCog, {\n                  className: \"h-6 w-6 text-purple-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 107,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 106,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"ml-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-600\",\n                  children: \"System Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 110,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-2xl font-semibold text-gray-900\",\n                  children: \"Online\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 111,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 109,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-xl shadow-xl p-8 border border-gray-100\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-3xl font-extrabold text-gray-800 mb-4\",\n            children: \"Selamat Datang di Admin Panel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 text-lg leading-relaxed mb-6\",\n            children: \"Gunakan menu navigasi di samping untuk mengelola berbagai aspek aplikasi HaloBantu. Sebagai admin, Anda memiliki akses penuh untuk mengelola pengguna dan sistem.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"border border-gray-200 rounded-lg p-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"font-semibold text-gray-900 mb-2\",\n                children: \"Kelola Pengguna\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 126,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600\",\n                children: \"Tambah, edit, atau hapus akun pengguna dan admin\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 127,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"border border-gray-200 rounded-lg p-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"font-semibold text-gray-900 mb-2\",\n                children: \"Pengaturan Sistem\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 130,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600\",\n                children: \"Konfigurasi pengaturan aplikasi dan keamanan\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 131,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"border border-gray-200 rounded-lg p-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"font-semibold text-gray-900 mb-2\",\n                children: \"Laporan & Analytics\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 134,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600\",\n                children: \"Lihat statistik penggunaan dan laporan sistem\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 135,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"border border-gray-200 rounded-lg p-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"font-semibold text-gray-900 mb-2\",\n                children: \"Monitoring\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 138,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600\",\n                children: \"Monitor aktivitas sistem dan keamanan\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 139,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 23,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminDashboard, \"ZmJpvzBBf8J7VCgSKqUUk0eHjAY=\", false, function () {\n  return [useLocation, useNavigate];\n});\n_c = AdminDashboard;\nexport default AdminDashboard;\nvar _c;\n$RefreshReg$(_c, \"AdminDashboard\");", "map": {"version": 3, "names": ["React", "Link", "useLocation", "useNavigate", "FaUsers", "FaCog", "FaChartBar", "FaSignOutAlt", "FaUserShield", "authService", "jsxDEV", "_jsxDEV", "AdminDashboard", "_s", "location", "navigate", "currentUser", "getCurrentUser", "navLinks", "path", "label", "icon", "handleLogout", "logout", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "link", "IconComponent", "to", "pathname", "firstName", "username", "role", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/JOKI MAKALAH/halobantu1/halobantu/src/features/admin/AdminDashboard.js"], "sourcesContent": ["import React from 'react';\r\nimport { Link, useLocation, useNavigate } from 'react-router-dom';\r\nimport { FaUsers, FaCog, FaChartBar, FaSignOutAlt, FaUserShield } from 'react-icons/fa';\r\nimport authService from '../../services/authService';\r\n\r\nconst AdminDashboard = () => {\r\n  const location = useLocation();\r\n  const navigate = useNavigate();\r\n  const currentUser = authService.getCurrentUser();\r\n\r\n  const navLinks = [\r\n    { path: '/admin/users', label: 'Kelola Pengguna', icon: FaUsers },\r\n    { path: '/admin/settings', label: 'Pengaturan', icon: FaCog },\r\n    { path: '/admin/reports', label: 'Laporan', icon: FaChartBar },\r\n  ];\r\n\r\n  const handleLogout = async () => {\r\n    await authService.logout();\r\n    navigate('/login');\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex\">\r\n      {/* Sidebar */}\r\n      <div className=\"w-64 bg-gray-800 text-white flex flex-col shadow-lg\">\r\n        <div className=\"p-6 border-b border-gray-700\">\r\n          <div className=\"flex items-center\">\r\n            <FaUserShield className=\"h-8 w-8 text-blue-300 mr-3\" />\r\n            <div>\r\n              <h2 className=\"text-xl font-bold text-blue-300\">HaloBantu</h2>\r\n              <p className=\"text-sm text-gray-300\">Admin Panel</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <nav className=\"flex-1 px-4 py-8 space-y-3\">\r\n          {navLinks.map((link) => {\r\n            const IconComponent = link.icon;\r\n            return (\r\n              <Link\r\n                key={link.path}\r\n                to={link.path}\r\n                className={`flex items-center px-5 py-3 rounded-lg text-lg font-medium transition-all duration-300\r\n                  ${location.pathname === link.path\r\n                    ? 'bg-blue-600 text-white shadow-md'\r\n                    : 'hover:bg-gray-700 hover:text-blue-200'\r\n                  }`}\r\n              >\r\n                <IconComponent className=\"h-5 w-5 mr-3\" />\r\n                {link.label}\r\n              </Link>\r\n            );\r\n          })}\r\n        </nav>\r\n      </div>\r\n\r\n      {/* Main Content */}\r\n      <div className=\"flex-1 flex flex-col\">\r\n        <header className=\"bg-white shadow-md p-6 border-b border-gray-200 flex justify-between items-center\">\r\n          <h1 className=\"text-3xl font-bold text-gray-800\">Admin Dashboard</h1>\r\n          <div className=\"flex items-center space-x-4\">\r\n            <div className=\"flex items-center text-sm text-gray-700\">\r\n              <FaUserShield className=\"h-4 w-4 mr-2\" />\r\n              <span>Halo, {currentUser?.firstName || currentUser?.username}!</span>\r\n              <span className=\"ml-2 px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full\">\r\n                {currentUser?.role}\r\n              </span>\r\n            </div>\r\n            <button\r\n              onClick={handleLogout}\r\n              className=\"flex items-center px-4 py-2 text-sm text-red-600 bg-red-100 rounded-md hover:bg-red-200 transition-colors font-semibold\"\r\n            >\r\n              <FaSignOutAlt className=\"h-4 w-4 mr-1\" />\r\n              Logout\r\n            </button>\r\n          </div>\r\n        </header>\r\n        <main className=\"flex-1 p-10\">\r\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\">\r\n            {/* Stats Cards */}\r\n            <div className=\"bg-white rounded-lg shadow p-6\">\r\n              <div className=\"flex items-center\">\r\n                <div className=\"p-3 rounded-full bg-blue-100\">\r\n                  <FaUsers className=\"h-6 w-6 text-blue-600\" />\r\n                </div>\r\n                <div className=\"ml-4\">\r\n                  <p className=\"text-sm font-medium text-gray-600\">Total Users</p>\r\n                  <p className=\"text-2xl font-semibold text-gray-900\">2</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"bg-white rounded-lg shadow p-6\">\r\n              <div className=\"flex items-center\">\r\n                <div className=\"p-3 rounded-full bg-green-100\">\r\n                  <FaChartBar className=\"h-6 w-6 text-green-600\" />\r\n                </div>\r\n                <div className=\"ml-4\">\r\n                  <p className=\"text-sm font-medium text-gray-600\">Active Sessions</p>\r\n                  <p className=\"text-2xl font-semibold text-gray-900\">1</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"bg-white rounded-lg shadow p-6\">\r\n              <div className=\"flex items-center\">\r\n                <div className=\"p-3 rounded-full bg-purple-100\">\r\n                  <FaCog className=\"h-6 w-6 text-purple-600\" />\r\n                </div>\r\n                <div className=\"ml-4\">\r\n                  <p className=\"text-sm font-medium text-gray-600\">System Status</p>\r\n                  <p className=\"text-2xl font-semibold text-gray-900\">Online</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"bg-white rounded-xl shadow-xl p-8 border border-gray-100\">\r\n            <h2 className=\"text-3xl font-extrabold text-gray-800 mb-4\">Selamat Datang di Admin Panel</h2>\r\n            <p className=\"text-gray-600 text-lg leading-relaxed mb-6\">\r\n              Gunakan menu navigasi di samping untuk mengelola berbagai aspek aplikasi HaloBantu.\r\n              Sebagai admin, Anda memiliki akses penuh untuk mengelola pengguna dan sistem.\r\n            </p>\r\n\r\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n              <div className=\"border border-gray-200 rounded-lg p-4\">\r\n                <h3 className=\"font-semibold text-gray-900 mb-2\">Kelola Pengguna</h3>\r\n                <p className=\"text-sm text-gray-600\">Tambah, edit, atau hapus akun pengguna dan admin</p>\r\n              </div>\r\n              <div className=\"border border-gray-200 rounded-lg p-4\">\r\n                <h3 className=\"font-semibold text-gray-900 mb-2\">Pengaturan Sistem</h3>\r\n                <p className=\"text-sm text-gray-600\">Konfigurasi pengaturan aplikasi dan keamanan</p>\r\n              </div>\r\n              <div className=\"border border-gray-200 rounded-lg p-4\">\r\n                <h3 className=\"font-semibold text-gray-900 mb-2\">Laporan & Analytics</h3>\r\n                <p className=\"text-sm text-gray-600\">Lihat statistik penggunaan dan laporan sistem</p>\r\n              </div>\r\n              <div className=\"border border-gray-200 rounded-lg p-4\">\r\n                <h3 className=\"font-semibold text-gray-900 mb-2\">Monitoring</h3>\r\n                <p className=\"text-sm text-gray-600\">Monitor aktivitas sistem dan keamanan</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </main>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default AdminDashboard; "], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACjE,SAASC,OAAO,EAAEC,KAAK,EAAEC,UAAU,EAAEC,YAAY,EAAEC,YAAY,QAAQ,gBAAgB;AACvF,OAAOC,WAAW,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,QAAQ,GAAGZ,WAAW,CAAC,CAAC;EAC9B,MAAMa,QAAQ,GAAGZ,WAAW,CAAC,CAAC;EAC9B,MAAMa,WAAW,GAAGP,WAAW,CAACQ,cAAc,CAAC,CAAC;EAEhD,MAAMC,QAAQ,GAAG,CACf;IAAEC,IAAI,EAAE,cAAc;IAAEC,KAAK,EAAE,iBAAiB;IAAEC,IAAI,EAAEjB;EAAQ,CAAC,EACjE;IAAEe,IAAI,EAAE,iBAAiB;IAAEC,KAAK,EAAE,YAAY;IAAEC,IAAI,EAAEhB;EAAM,CAAC,EAC7D;IAAEc,IAAI,EAAE,gBAAgB;IAAEC,KAAK,EAAE,SAAS;IAAEC,IAAI,EAAEf;EAAW,CAAC,CAC/D;EAED,MAAMgB,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,MAAMb,WAAW,CAACc,MAAM,CAAC,CAAC;IAC1BR,QAAQ,CAAC,QAAQ,CAAC;EACpB,CAAC;EAED,oBACEJ,OAAA;IAAKa,SAAS,EAAC,gEAAgE;IAAAC,QAAA,gBAE7Ed,OAAA;MAAKa,SAAS,EAAC,qDAAqD;MAAAC,QAAA,gBAClEd,OAAA;QAAKa,SAAS,EAAC,8BAA8B;QAAAC,QAAA,eAC3Cd,OAAA;UAAKa,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCd,OAAA,CAACH,YAAY;YAACgB,SAAS,EAAC;UAA4B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACvDlB,OAAA;YAAAc,QAAA,gBACEd,OAAA;cAAIa,SAAS,EAAC,iCAAiC;cAAAC,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9DlB,OAAA;cAAGa,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNlB,OAAA;QAAKa,SAAS,EAAC,4BAA4B;QAAAC,QAAA,EACxCP,QAAQ,CAACY,GAAG,CAAEC,IAAI,IAAK;UACtB,MAAMC,aAAa,GAAGD,IAAI,CAACV,IAAI;UAC/B,oBACEV,OAAA,CAACV,IAAI;YAEHgC,EAAE,EAAEF,IAAI,CAACZ,IAAK;YACdK,SAAS,EAAE;AAC3B,oBAAoBV,QAAQ,CAACoB,QAAQ,KAAKH,IAAI,CAACZ,IAAI,GAC7B,kCAAkC,GAClC,uCAAuC,EACxC;YAAAM,QAAA,gBAELd,OAAA,CAACqB,aAAa;cAACR,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EACzCE,IAAI,CAACX,KAAK;UAAA,GATNW,IAAI,CAACZ,IAAI;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAUV,CAAC;QAEX,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNlB,OAAA;MAAKa,SAAS,EAAC,sBAAsB;MAAAC,QAAA,gBACnCd,OAAA;QAAQa,SAAS,EAAC,mFAAmF;QAAAC,QAAA,gBACnGd,OAAA;UAAIa,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrElB,OAAA;UAAKa,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1Cd,OAAA;YAAKa,SAAS,EAAC,yCAAyC;YAAAC,QAAA,gBACtDd,OAAA,CAACH,YAAY;cAACgB,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACzClB,OAAA;cAAAc,QAAA,GAAM,QAAM,EAAC,CAAAT,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEmB,SAAS,MAAInB,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEoB,QAAQ,GAAC,GAAC;YAAA;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACrElB,OAAA;cAAMa,SAAS,EAAC,6DAA6D;cAAAC,QAAA,EAC1ET,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEqB;YAAI;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACNlB,OAAA;YACE2B,OAAO,EAAEhB,YAAa;YACtBE,SAAS,EAAC,yHAAyH;YAAAC,QAAA,gBAEnId,OAAA,CAACJ,YAAY;cAACiB,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,UAE3C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eACTlB,OAAA;QAAMa,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC3Bd,OAAA;UAAKa,SAAS,EAAC,4CAA4C;UAAAC,QAAA,gBAEzDd,OAAA;YAAKa,SAAS,EAAC,gCAAgC;YAAAC,QAAA,eAC7Cd,OAAA;cAAKa,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCd,OAAA;gBAAKa,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,eAC3Cd,OAAA,CAACP,OAAO;kBAACoB,SAAS,EAAC;gBAAuB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CAAC,eACNlB,OAAA;gBAAKa,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnBd,OAAA;kBAAGa,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAChElB,OAAA;kBAAGa,SAAS,EAAC,sCAAsC;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENlB,OAAA;YAAKa,SAAS,EAAC,gCAAgC;YAAAC,QAAA,eAC7Cd,OAAA;cAAKa,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCd,OAAA;gBAAKa,SAAS,EAAC,+BAA+B;gBAAAC,QAAA,eAC5Cd,OAAA,CAACL,UAAU;kBAACkB,SAAS,EAAC;gBAAwB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC,eACNlB,OAAA;gBAAKa,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnBd,OAAA;kBAAGa,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACpElB,OAAA;kBAAGa,SAAS,EAAC,sCAAsC;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENlB,OAAA;YAAKa,SAAS,EAAC,gCAAgC;YAAAC,QAAA,eAC7Cd,OAAA;cAAKa,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCd,OAAA;gBAAKa,SAAS,EAAC,gCAAgC;gBAAAC,QAAA,eAC7Cd,OAAA,CAACN,KAAK;kBAACmB,SAAS,EAAC;gBAAyB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CAAC,eACNlB,OAAA;gBAAKa,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnBd,OAAA;kBAAGa,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAClElB,OAAA;kBAAGa,SAAS,EAAC,sCAAsC;kBAAAC,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENlB,OAAA;UAAKa,SAAS,EAAC,0DAA0D;UAAAC,QAAA,gBACvEd,OAAA;YAAIa,SAAS,EAAC,4CAA4C;YAAAC,QAAA,EAAC;UAA6B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7FlB,OAAA;YAAGa,SAAS,EAAC,4CAA4C;YAAAC,QAAA,EAAC;UAG1D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAEJlB,OAAA;YAAKa,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpDd,OAAA;cAAKa,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpDd,OAAA;gBAAIa,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrElB,OAAA;gBAAGa,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAgD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtF,CAAC,eACNlB,OAAA;cAAKa,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpDd,OAAA;gBAAIa,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAAC;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvElB,OAAA;gBAAGa,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAA4C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClF,CAAC,eACNlB,OAAA;cAAKa,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpDd,OAAA;gBAAIa,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAAC;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzElB,OAAA;gBAAGa,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAA6C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnF,CAAC,eACNlB,OAAA;cAAKa,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpDd,OAAA;gBAAIa,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChElB,OAAA;gBAAGa,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAqC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAChB,EAAA,CA7IID,cAAc;EAAA,QACDV,WAAW,EACXC,WAAW;AAAA;AAAAoC,EAAA,GAFxB3B,cAAc;AA+IpB,eAAeA,cAAc;AAAC,IAAA2B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}