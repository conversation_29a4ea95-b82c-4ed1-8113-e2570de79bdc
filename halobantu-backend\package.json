{"name": "halobantu-backend", "version": "1.0.0", "description": "Backend API for HaloBantu - Psychology Consultation Platform", "main": "server.js", "scripts": {"start": "node server.js", "dev": "node server.js", "dev:watch": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1", "db:create": "node scripts/createDatabase.js", "db:sync": "node scripts/syncDatabase.js", "db:sync:force": "node scripts/syncDatabase.js --force", "db:sync:alter": "node scripts/syncDatabase.js --alter", "setup": "npm run db:create && npm run db:sync"}, "keywords": ["nodejs", "express", "mysql", "jwt", "authentication", "psychology", "consultation"], "author": "HaloBantu Team", "license": "ISC", "type": "commonjs", "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "express-rate-limit": "^7.5.0", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "mysql2": "^3.14.1", "sequelize": "^6.37.7"}, "devDependencies": {"nodemon": "^3.0.0"}}