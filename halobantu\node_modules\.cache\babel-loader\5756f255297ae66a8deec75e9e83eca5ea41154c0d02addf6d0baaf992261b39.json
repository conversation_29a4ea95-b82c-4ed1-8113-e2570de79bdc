{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\JOKI MAKALAH\\\\halobantu1\\\\halobantu\\\\src\\\\components\\\\common\\\\ProtectedRoute.js\";\nimport React from 'react';\nimport { Navigate } from 'react-router-dom';\nimport authService from '../../services/authService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProtectedRoute = ({\n  children,\n  requiredRole = null\n}) => {\n  const isAuthenticated = authService.isAuthenticated();\n  const currentUser = authService.getCurrentUser();\n  console.log('🔒 ProtectedRoute check:', {\n    isAuthenticated,\n    currentUser,\n    requiredRole\n  });\n\n  // Jika tidak login, redirect ke login\n  if (!isAuthenticated) {\n    console.log('❌ Not authenticated, redirecting to login');\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/login\",\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 12\n    }, this);\n  }\n\n  // Jika ada role requirement dan user tidak memiliki role yang sesuai\n  if (requiredRole && (currentUser === null || currentUser === void 0 ? void 0 : currentUser.role) !== requiredRole) {\n    console.log(`❌ Role mismatch. Required: ${requiredRole}, User: ${currentUser === null || currentUser === void 0 ? void 0 : currentUser.role}`);\n    // Redirect berdasarkan role user\n    if ((currentUser === null || currentUser === void 0 ? void 0 : currentUser.role) === 'admin') {\n      console.log('🔄 Redirecting admin to /admin');\n      return /*#__PURE__*/_jsxDEV(Navigate, {\n        to: \"/admin\",\n        replace: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 14\n      }, this);\n    } else {\n      console.log('🔄 Redirecting user to /dashboard');\n      return /*#__PURE__*/_jsxDEV(Navigate, {\n        to: \"/dashboard\",\n        replace: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 14\n      }, this);\n    }\n  }\n  console.log('✅ Access granted');\n  return children;\n};\n_c = ProtectedRoute;\nexport default ProtectedRoute;\nvar _c;\n$RefreshReg$(_c, \"ProtectedRoute\");", "map": {"version": 3, "names": ["React", "Navigate", "authService", "jsxDEV", "_jsxDEV", "ProtectedRoute", "children", "requiredRole", "isAuthenticated", "currentUser", "getCurrentUser", "console", "log", "to", "replace", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "role", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/JOKI MAKALAH/halobantu1/halobantu/src/components/common/ProtectedRoute.js"], "sourcesContent": ["import React from 'react';\nimport { Navigate } from 'react-router-dom';\nimport authService from '../../services/authService';\n\nconst ProtectedRoute = ({ children, requiredRole = null }) => {\n  const isAuthenticated = authService.isAuthenticated();\n  const currentUser = authService.getCurrentUser();\n\n  console.log('🔒 ProtectedRoute check:', {\n    isAuthenticated,\n    currentUser,\n    requiredRole\n  });\n\n  // Jika tidak login, redirect ke login\n  if (!isAuthenticated) {\n    console.log('❌ Not authenticated, redirecting to login');\n    return <Navigate to=\"/login\" replace />;\n  }\n\n  // Jika ada role requirement dan user tidak memiliki role yang sesuai\n  if (requiredRole && currentUser?.role !== requiredRole) {\n    console.log(`❌ Role mismatch. Required: ${requiredRole}, User: ${currentUser?.role}`);\n    // Redirect berdasarkan role user\n    if (currentUser?.role === 'admin') {\n      console.log('🔄 Redirecting admin to /admin');\n      return <Navigate to=\"/admin\" replace />;\n    } else {\n      console.log('🔄 Redirecting user to /dashboard');\n      return <Navigate to=\"/dashboard\" replace />;\n    }\n  }\n\n  console.log('✅ Access granted');\n  return children;\n};\n\nexport default ProtectedRoute;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,OAAOC,WAAW,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,MAAMC,cAAc,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,YAAY,GAAG;AAAK,CAAC,KAAK;EAC5D,MAAMC,eAAe,GAAGN,WAAW,CAACM,eAAe,CAAC,CAAC;EACrD,MAAMC,WAAW,GAAGP,WAAW,CAACQ,cAAc,CAAC,CAAC;EAEhDC,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE;IACtCJ,eAAe;IACfC,WAAW;IACXF;EACF,CAAC,CAAC;;EAEF;EACA,IAAI,CAACC,eAAe,EAAE;IACpBG,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;IACxD,oBAAOR,OAAA,CAACH,QAAQ;MAACY,EAAE,EAAC,QAAQ;MAACC,OAAO;IAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACzC;;EAEA;EACA,IAAIX,YAAY,IAAI,CAAAE,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEU,IAAI,MAAKZ,YAAY,EAAE;IACtDI,OAAO,CAACC,GAAG,CAAC,8BAA8BL,YAAY,WAAWE,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEU,IAAI,EAAE,CAAC;IACrF;IACA,IAAI,CAAAV,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEU,IAAI,MAAK,OAAO,EAAE;MACjCR,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;MAC7C,oBAAOR,OAAA,CAACH,QAAQ;QAACY,EAAE,EAAC,QAAQ;QAACC,OAAO;MAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IACzC,CAAC,MAAM;MACLP,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;MAChD,oBAAOR,OAAA,CAACH,QAAQ;QAACY,EAAE,EAAC,YAAY;QAACC,OAAO;MAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAC7C;EACF;EAEAP,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;EAC/B,OAAON,QAAQ;AACjB,CAAC;AAACc,EAAA,GA/BIf,cAAc;AAiCpB,eAAeA,cAAc;AAAC,IAAAe,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}