{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\JOKI MAKALAH\\\\halobantu1\\\\halobantu\\\\src\\\\features\\\\psychology\\\\pages\\\\DoctorBiography.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { FaArrowLeft, FaGraduationCap, FaBriefcase, FaMapMarkerAlt } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DoctorBiography = () => {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const {\n    doctor\n  } = location.state || {};\n\n  // Default doctor data if none provided\n  const defaultDoctor = {\n    id: 1,\n    name: 'Dr. Fauzan Hamid',\n    specialization: 'Pengalaman 4 tahun',\n    image: 'https://images.pexels.com/photos/5327921/pexels-photo-5327921.jpeg?auto=compress&cs=tinysrgb&w=400',\n    bio: 'Kami siap mendampingi perjalanan Anda menuju kesehatan emosional. Dapatkan bimbingan dari psikolog profesional dan temukan solusi terbaik untuk kesehatan mental Anda.',\n    clinic: 'Klinik ABC Surabaya Utara',\n    education: 'Universitas Airlangga, 2015',\n    experience: '4 tahun',\n    services: ['Bengkel', 'Konsultasi Psikologi', 'Opo Wae'],\n    links: ['Home', 'Layanan', 'Contact', 'Login', 'Cart']\n  };\n  const doctorData = {\n    ...defaultDoctor,\n    ...doctor,\n    services: (doctor === null || doctor === void 0 ? void 0 : doctor.services) || defaultDoctor.services,\n    links: (doctor === null || doctor === void 0 ? void 0 : doctor.links) || defaultDoctor.links\n  };\n  const handleBack = () => {\n    navigate(-1);\n  };\n  const handleBookConsultation = () => {\n    navigate('/psychology/consultation', {\n      state: {\n        psychologist: doctorData\n      }\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow-sm\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-6xl mx-auto px-4 py-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-xl font-bold text-gray-800\",\n              children: \"Future X\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 56,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-600\",\n              children: \"Minibiografi dokter\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 57,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 55,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n            className: \"flex items-center space-x-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#\",\n              className: \"text-gray-600 hover:text-gray-800\",\n              children: \"Home\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 60,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#\",\n              className: \"text-gray-600 hover:text-gray-800\",\n              children: \"Layanan\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 61,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#\",\n              className: \"text-gray-600 hover:text-gray-800\",\n              children: \"Contact\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 62,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-gray-600\",\n                children: \"\\uD83D\\uDED2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 64,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-gray-600\",\n                children: \"\\uD83D\\uDC64\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 65,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 63,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative bg-gradient-to-r from-blue-50 to-indigo-100 py-16\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 bg-black bg-opacity-20\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative max-w-6xl mx-auto px-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center mb-6\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleBack,\n            className: \"flex items-center text-gray-700 hover:text-gray-900 mr-4\",\n            children: [/*#__PURE__*/_jsxDEV(FaArrowLeft, {\n              className: \"mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 81,\n              columnNumber: 15\n            }, this), \"Kembali\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white bg-opacity-90 backdrop-blur-sm rounded-xl p-8 max-w-2xl\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-3xl font-bold text-gray-800 mb-4\",\n            children: \"Konsultasi Psikologi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 leading-relaxed\",\n            children: doctorData.bio\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"py-16\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-6xl mx-auto px-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid md:grid-cols-2 gap-12 items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: doctorData.image,\n                alt: doctorData.name,\n                className: \"w-80 h-96 object-cover rounded-lg shadow-lg\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 104,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 103,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-3xl font-bold text-gray-800 mb-2\",\n                children: doctorData.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 115,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-lg text-gray-600 mb-6\",\n                children: doctorData.specialization\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 118,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gray-100 rounded-lg p-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"font-semibold text-gray-800 mb-2\",\n                children: \"Tempat Praktik\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 125,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center text-gray-600\",\n                children: [/*#__PURE__*/_jsxDEV(FaMapMarkerAlt, {\n                  className: \"mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 127,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: doctorData.clinic\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 128,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 126,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 124,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gray-100 rounded-lg p-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"font-semibold text-gray-800 mb-2\",\n                children: \"Alumni\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 134,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center text-gray-600\",\n                children: [/*#__PURE__*/_jsxDEV(FaGraduationCap, {\n                  className: \"mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 136,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: doctorData.education\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 137,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 135,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleBookConsultation,\n              className: \"w-full bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition-colors font-semibold\",\n              children: \"Booking Konsultasi\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 98,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"footer\", {\n      className: \"bg-gray-800 text-white py-12\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-6xl mx-auto px-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid md:grid-cols-3 gap-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-bold mb-4\",\n              children: \"Future X\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-300 text-sm leading-relaxed\",\n              children: \"Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex space-x-4 mt-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#\",\n                className: \"text-gray-300 hover:text-white\",\n                children: \"\\uD83D\\uDCD8\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 164,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#\",\n                className: \"text-gray-300 hover:text-white\",\n                children: \"\\uD83D\\uDCF7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 165,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#\",\n                className: \"text-gray-300 hover:text-white\",\n                children: \"\\uD83D\\uDCFA\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 166,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"font-semibold mb-4\",\n              children: \"Our Services\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              className: \"space-y-2 text-gray-300 text-sm\",\n              children: (doctorData.services || []).map((service, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n                children: [\"\\u25B6 \", service]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 175,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"font-semibold mb-4\",\n              children: \"Useful Links\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              className: \"space-y-2 text-gray-300 text-sm\",\n              children: (doctorData.links || []).map((link, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n                children: [\"\\u25B6 \", link]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 155,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 154,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 50,\n    columnNumber: 5\n  }, this);\n};\n_s(DoctorBiography, \"VDZHUspDq9N5O9RWjniBrjgIdAA=\", false, function () {\n  return [useNavigate, useLocation];\n});\n_c = DoctorBiography;\nexport default DoctorBiography;\nvar _c;\n$RefreshReg$(_c, \"DoctorBiography\");", "map": {"version": 3, "names": ["React", "useNavigate", "useLocation", "FaArrowLeft", "FaGraduationCap", "FaBriefcase", "FaMapMarkerAlt", "jsxDEV", "_jsxDEV", "DoctorBiography", "_s", "navigate", "location", "doctor", "state", "defaultDoctor", "id", "name", "specialization", "image", "bio", "clinic", "education", "experience", "services", "links", "<PERSON><PERSON><PERSON>", "handleBack", "handleBookConsultation", "psychologist", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "href", "onClick", "src", "alt", "map", "service", "index", "link", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/JOKI MAKALAH/halobantu1/halobantu/src/features/psychology/pages/DoctorBiography.js"], "sourcesContent": ["import React from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { FaArrowLeft, FaGraduationCap, FaBriefcase, FaMapMarkerAlt } from 'react-icons/fa';\n\nconst DoctorBiography = () => {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const { doctor } = location.state || {};\n\n  // Default doctor data if none provided\n  const defaultDoctor = {\n    id: 1,\n    name: 'Dr. <PERSON><PERSON><PERSON>',\n    specialization: 'Pengalaman 4 tahun',\n    image: 'https://images.pexels.com/photos/5327921/pexels-photo-5327921.jpeg?auto=compress&cs=tinysrgb&w=400',\n    bio: 'Kami siap mendampingi perjalanan Anda menuju kesehatan emosional. Dapatkan bimbingan dari psikolog profesional dan temukan solusi terbaik untuk kesehatan mental Anda.',\n    clinic: 'Klinik ABC Surabaya Utara',\n    education: 'Universitas Airlangga, 2015',\n    experience: '4 tahun',\n    services: [\n      'Beng<PERSON>',\n      'Konsultasi Psikologi',\n      'Op<PERSON> Wae'\n    ],\n    links: [\n      'Home',\n      'Layanan',\n      'Contact',\n      'Login',\n      'Cart'\n    ]\n  };\n\n  const doctorData = {\n    ...defaultDoctor,\n    ...doctor,\n    services: doctor?.services || defaultDoctor.services,\n    links: doctor?.links || defaultDoctor.links\n  };\n\n  const handleBack = () => {\n    navigate(-1);\n  };\n\n  const handleBookConsultation = () => {\n    navigate('/psychology/consultation', { state: { psychologist: doctorData } });\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <div className=\"bg-white shadow-sm\">\n        <div className=\"max-w-6xl mx-auto px-4 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-8\">\n              <h1 className=\"text-xl font-bold text-gray-800\">Future X</h1>\n              <p className=\"text-sm text-gray-600\">Minibiografi dokter</p>\n            </div>\n            <nav className=\"flex items-center space-x-6\">\n              <a href=\"#\" className=\"text-gray-600 hover:text-gray-800\">Home</a>\n              <a href=\"#\" className=\"text-gray-600 hover:text-gray-800\">Layanan</a>\n              <a href=\"#\" className=\"text-gray-600 hover:text-gray-800\">Contact</a>\n              <div className=\"flex items-center space-x-2\">\n                <span className=\"text-gray-600\">🛒</span>\n                <span className=\"text-gray-600\">👤</span>\n              </div>\n            </nav>\n          </div>\n        </div>\n      </div>\n\n      {/* Hero Section */}\n      <div className=\"relative bg-gradient-to-r from-blue-50 to-indigo-100 py-16\">\n        <div className=\"absolute inset-0 bg-black bg-opacity-20\"></div>\n        <div className=\"relative max-w-6xl mx-auto px-4\">\n          <div className=\"flex items-center mb-6\">\n            <button\n              onClick={handleBack}\n              className=\"flex items-center text-gray-700 hover:text-gray-900 mr-4\"\n            >\n              <FaArrowLeft className=\"mr-2\" />\n              Kembali\n            </button>\n          </div>\n          \n          <div className=\"bg-white bg-opacity-90 backdrop-blur-sm rounded-xl p-8 max-w-2xl\">\n            <h1 className=\"text-3xl font-bold text-gray-800 mb-4\">\n              Konsultasi Psikologi\n            </h1>\n            <p className=\"text-gray-600 leading-relaxed\">\n              {doctorData.bio}\n            </p>\n          </div>\n        </div>\n      </div>\n\n      {/* Doctor Profile Section */}\n      <div className=\"py-16\">\n        <div className=\"max-w-6xl mx-auto px-4\">\n          <div className=\"grid md:grid-cols-2 gap-12 items-center\">\n            {/* Doctor Image */}\n            <div className=\"flex justify-center\">\n              <div className=\"relative\">\n                <img\n                  src={doctorData.image}\n                  alt={doctorData.name}\n                  className=\"w-80 h-96 object-cover rounded-lg shadow-lg\"\n                />\n              </div>\n            </div>\n\n            {/* Doctor Information */}\n            <div className=\"space-y-6\">\n              <div>\n                <h2 className=\"text-3xl font-bold text-gray-800 mb-2\">\n                  {doctorData.name}\n                </h2>\n                <p className=\"text-lg text-gray-600 mb-6\">\n                  {doctorData.specialization}\n                </p>\n              </div>\n\n              {/* Practice Location */}\n              <div className=\"bg-gray-100 rounded-lg p-4\">\n                <h3 className=\"font-semibold text-gray-800 mb-2\">Tempat Praktik</h3>\n                <div className=\"flex items-center text-gray-600\">\n                  <FaMapMarkerAlt className=\"mr-2\" />\n                  <span>{doctorData.clinic}</span>\n                </div>\n              </div>\n\n              {/* Education */}\n              <div className=\"bg-gray-100 rounded-lg p-4\">\n                <h3 className=\"font-semibold text-gray-800 mb-2\">Alumni</h3>\n                <div className=\"flex items-center text-gray-600\">\n                  <FaGraduationCap className=\"mr-2\" />\n                  <span>{doctorData.education}</span>\n                </div>\n              </div>\n\n              {/* Book Consultation Button */}\n              <button\n                onClick={handleBookConsultation}\n                className=\"w-full bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition-colors font-semibold\"\n              >\n                Booking Konsultasi\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Footer */}\n      <footer className=\"bg-gray-800 text-white py-12\">\n        <div className=\"max-w-6xl mx-auto px-4\">\n          <div className=\"grid md:grid-cols-3 gap-8\">\n            {/* Company Info */}\n            <div>\n              <h3 className=\"text-xl font-bold mb-4\">Future X</h3>\n              <p className=\"text-gray-300 text-sm leading-relaxed\">\n                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.\n              </p>\n              <div className=\"flex space-x-4 mt-4\">\n                <a href=\"#\" className=\"text-gray-300 hover:text-white\">📘</a>\n                <a href=\"#\" className=\"text-gray-300 hover:text-white\">📷</a>\n                <a href=\"#\" className=\"text-gray-300 hover:text-white\">📺</a>\n              </div>\n            </div>\n\n            {/* Our Services */}\n            <div>\n              <h4 className=\"font-semibold mb-4\">Our Services</h4>\n              <ul className=\"space-y-2 text-gray-300 text-sm\">\n                {(doctorData.services || []).map((service, index) => (\n                  <li key={index}>▶ {service}</li>\n                ))}\n              </ul>\n            </div>\n\n            {/* Useful Links */}\n            <div>\n              <h4 className=\"font-semibold mb-4\">Useful Links</h4>\n              <ul className=\"space-y-2 text-gray-300 text-sm\">\n                {(doctorData.links || []).map((link, index) => (\n                  <li key={index}>▶ {link}</li>\n                ))}\n              </ul>\n            </div>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n};\n\nexport default DoctorBiography;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SAASC,WAAW,EAAEC,eAAe,EAAEC,WAAW,EAAEC,cAAc,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3F,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAMC,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B,MAAMW,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEW;EAAO,CAAC,GAAGD,QAAQ,CAACE,KAAK,IAAI,CAAC,CAAC;;EAEvC;EACA,MAAMC,aAAa,GAAG;IACpBC,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,kBAAkB;IACxBC,cAAc,EAAE,oBAAoB;IACpCC,KAAK,EAAE,oGAAoG;IAC3GC,GAAG,EAAE,wKAAwK;IAC7KC,MAAM,EAAE,2BAA2B;IACnCC,SAAS,EAAE,6BAA6B;IACxCC,UAAU,EAAE,SAAS;IACrBC,QAAQ,EAAE,CACR,SAAS,EACT,sBAAsB,EACtB,SAAS,CACV;IACDC,KAAK,EAAE,CACL,MAAM,EACN,SAAS,EACT,SAAS,EACT,OAAO,EACP,MAAM;EAEV,CAAC;EAED,MAAMC,UAAU,GAAG;IACjB,GAAGX,aAAa;IAChB,GAAGF,MAAM;IACTW,QAAQ,EAAE,CAAAX,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEW,QAAQ,KAAIT,aAAa,CAACS,QAAQ;IACpDC,KAAK,EAAE,CAAAZ,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEY,KAAK,KAAIV,aAAa,CAACU;EACxC,CAAC;EAED,MAAME,UAAU,GAAGA,CAAA,KAAM;IACvBhB,QAAQ,CAAC,CAAC,CAAC,CAAC;EACd,CAAC;EAED,MAAMiB,sBAAsB,GAAGA,CAAA,KAAM;IACnCjB,QAAQ,CAAC,0BAA0B,EAAE;MAAEG,KAAK,EAAE;QAAEe,YAAY,EAAEH;MAAW;IAAE,CAAC,CAAC;EAC/E,CAAC;EAED,oBACElB,OAAA;IAAKsB,SAAS,EAAC,yBAAyB;IAAAC,QAAA,gBAEtCvB,OAAA;MAAKsB,SAAS,EAAC,oBAAoB;MAAAC,QAAA,eACjCvB,OAAA;QAAKsB,SAAS,EAAC,6BAA6B;QAAAC,QAAA,eAC1CvB,OAAA;UAAKsB,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDvB,OAAA;YAAKsB,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1CvB,OAAA;cAAIsB,SAAS,EAAC,iCAAiC;cAAAC,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7D3B,OAAA;cAAGsB,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC,eACN3B,OAAA;YAAKsB,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1CvB,OAAA;cAAG4B,IAAI,EAAC,GAAG;cAACN,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAClE3B,OAAA;cAAG4B,IAAI,EAAC,GAAG;cAACN,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACrE3B,OAAA;cAAG4B,IAAI,EAAC,GAAG;cAACN,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACrE3B,OAAA;cAAKsB,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1CvB,OAAA;gBAAMsB,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACzC3B,OAAA;gBAAMsB,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN3B,OAAA;MAAKsB,SAAS,EAAC,4DAA4D;MAAAC,QAAA,gBACzEvB,OAAA;QAAKsB,SAAS,EAAC;MAAyC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC/D3B,OAAA;QAAKsB,SAAS,EAAC,iCAAiC;QAAAC,QAAA,gBAC9CvB,OAAA;UAAKsB,SAAS,EAAC,wBAAwB;UAAAC,QAAA,eACrCvB,OAAA;YACE6B,OAAO,EAAEV,UAAW;YACpBG,SAAS,EAAC,0DAA0D;YAAAC,QAAA,gBAEpEvB,OAAA,CAACL,WAAW;cAAC2B,SAAS,EAAC;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,WAElC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEN3B,OAAA;UAAKsB,SAAS,EAAC,kEAAkE;UAAAC,QAAA,gBAC/EvB,OAAA;YAAIsB,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EAAC;UAEtD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL3B,OAAA;YAAGsB,SAAS,EAAC,+BAA+B;YAAAC,QAAA,EACzCL,UAAU,CAACN;UAAG;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN3B,OAAA;MAAKsB,SAAS,EAAC,OAAO;MAAAC,QAAA,eACpBvB,OAAA;QAAKsB,SAAS,EAAC,wBAAwB;QAAAC,QAAA,eACrCvB,OAAA;UAAKsB,SAAS,EAAC,yCAAyC;UAAAC,QAAA,gBAEtDvB,OAAA;YAAKsB,SAAS,EAAC,qBAAqB;YAAAC,QAAA,eAClCvB,OAAA;cAAKsB,SAAS,EAAC,UAAU;cAAAC,QAAA,eACvBvB,OAAA;gBACE8B,GAAG,EAAEZ,UAAU,CAACP,KAAM;gBACtBoB,GAAG,EAAEb,UAAU,CAACT,IAAK;gBACrBa,SAAS,EAAC;cAA6C;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN3B,OAAA;YAAKsB,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBvB,OAAA;cAAAuB,QAAA,gBACEvB,OAAA;gBAAIsB,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,EAClDL,UAAU,CAACT;cAAI;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd,CAAC,eACL3B,OAAA;gBAAGsB,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EACtCL,UAAU,CAACR;cAAc;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAGN3B,OAAA;cAAKsB,SAAS,EAAC,4BAA4B;cAAAC,QAAA,gBACzCvB,OAAA;gBAAIsB,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpE3B,OAAA;gBAAKsB,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,gBAC9CvB,OAAA,CAACF,cAAc;kBAACwB,SAAS,EAAC;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACnC3B,OAAA;kBAAAuB,QAAA,EAAOL,UAAU,CAACL;gBAAM;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN3B,OAAA;cAAKsB,SAAS,EAAC,4BAA4B;cAAAC,QAAA,gBACzCvB,OAAA;gBAAIsB,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5D3B,OAAA;gBAAKsB,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,gBAC9CvB,OAAA,CAACJ,eAAe;kBAAC0B,SAAS,EAAC;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACpC3B,OAAA;kBAAAuB,QAAA,EAAOL,UAAU,CAACJ;gBAAS;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN3B,OAAA;cACE6B,OAAO,EAAET,sBAAuB;cAChCE,SAAS,EAAC,sGAAsG;cAAAC,QAAA,EACjH;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN3B,OAAA;MAAQsB,SAAS,EAAC,8BAA8B;MAAAC,QAAA,eAC9CvB,OAAA;QAAKsB,SAAS,EAAC,wBAAwB;QAAAC,QAAA,eACrCvB,OAAA;UAAKsB,SAAS,EAAC,2BAA2B;UAAAC,QAAA,gBAExCvB,OAAA;YAAAuB,QAAA,gBACEvB,OAAA;cAAIsB,SAAS,EAAC,wBAAwB;cAAAC,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpD3B,OAAA;cAAGsB,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAAC;YAErD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJ3B,OAAA;cAAKsB,SAAS,EAAC,qBAAqB;cAAAC,QAAA,gBAClCvB,OAAA;gBAAG4B,IAAI,EAAC,GAAG;gBAACN,SAAS,EAAC,gCAAgC;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC7D3B,OAAA;gBAAG4B,IAAI,EAAC,GAAG;gBAACN,SAAS,EAAC,gCAAgC;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC7D3B,OAAA;gBAAG4B,IAAI,EAAC,GAAG;gBAACN,SAAS,EAAC,gCAAgC;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN3B,OAAA;YAAAuB,QAAA,gBACEvB,OAAA;cAAIsB,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpD3B,OAAA;cAAIsB,SAAS,EAAC,iCAAiC;cAAAC,QAAA,EAC5C,CAACL,UAAU,CAACF,QAAQ,IAAI,EAAE,EAAEgB,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBAC9ClC,OAAA;gBAAAuB,QAAA,GAAgB,SAAE,EAACU,OAAO;cAAA,GAAjBC,KAAK;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAiB,CAChC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAGN3B,OAAA;YAAAuB,QAAA,gBACEvB,OAAA;cAAIsB,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpD3B,OAAA;cAAIsB,SAAS,EAAC,iCAAiC;cAAAC,QAAA,EAC5C,CAACL,UAAU,CAACD,KAAK,IAAI,EAAE,EAAEe,GAAG,CAAC,CAACG,IAAI,EAAED,KAAK,kBACxClC,OAAA;gBAAAuB,QAAA,GAAgB,SAAE,EAACY,IAAI;cAAA,GAAdD,KAAK;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAc,CAC7B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACzB,EAAA,CA7LID,eAAe;EAAA,QACFR,WAAW,EACXC,WAAW;AAAA;AAAA0C,EAAA,GAFxBnC,eAAe;AA+LrB,eAAeA,eAAe;AAAC,IAAAmC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}