import React from 'react';
import { FaCalendar<PERSON>lt, <PERSON>a<PERSON><PERSON>, FaUser } from 'react-icons/fa';

const DoctorScheduleList = ({ psychologists }) => {
  // Format jadwal untuk setiap dokter
  const formatSchedules = (psychologist) => {
    return [
      {
        id: 1,
        day: 'Senin',
        time: '09:00 - 10:00',
        isAvailable: true
      },
      {
        id: 2,
        day: 'Senin',
        time: '10:00 - 11:00',
        isAvailable: false
      },
      {
        id: 3,
        day: 'Senin',
        time: '11:00 - 12:00',
        isAvailable: true
      },
      {
        id: 4,
        day: 'Rabu',
        time: '09:00 - 10:00',
        isAvailable: true
      },
      {
        id: 5,
        day: '<PERSON>bu',
        time: '10:00 - 11:00',
        isAvailable: true
      },
      {
        id: 6,
        day: 'Jumat',
        time: '09:00 - 10:00',
        isAvailable: false
      },
      {
        id: 7,
        day: 'Jumat',
        time: '10:00 - 11:00',
        isAvailable: true
      }
    ];
  };

  return (
    <div className="mt-12">
      <h2 className="text-2xl font-bold text-gray-800 mb-6">
        <PERSON><PERSON><PERSON> Konsultasi
      </h2>
      
      <div className="space-y-8">
        {psychologists.map((psychologist) => (
          <div key={psychologist.id} className="bg-white rounded-xl shadow-lg p-6">
            {/* Header Dokter */}
            <div className="flex items-center space-x-4 mb-6">
              <img
                src={psychologist.image}
                alt={psychologist.name}
                className="w-16 h-16 rounded-full object-cover"
              />
              <div>
                <h3 className="text-xl font-semibold text-gray-800">
                  {psychologist.name}
                </h3>
                <p className="text-gray-600">{psychologist.specialization}</p>
              </div>
            </div>

            {/* Jadwal */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {formatSchedules(psychologist).map((schedule) => (
                <div
                  key={schedule.id}
                  className={`p-4 rounded-lg border ${
                    schedule.isAvailable
                      ? 'border-blue-200 bg-blue-50'
                      : 'border-gray-200 bg-gray-50'
                  }`}
                >
                  <div className="flex items-center space-x-2 text-gray-600 mb-2">
                    <FaCalendarAlt className="text-blue-500" />
                    <span>{schedule.day}</span>
                  </div>
                  <div className="flex items-center space-x-2 text-gray-600">
                    <FaClock className="text-blue-500" />
                    <span>{schedule.time}</span>
                  </div>
                  <div className="mt-2">
                    <span
                      className={`text-sm px-2 py-1 rounded-full ${
                        schedule.isAvailable
                          ? 'bg-green-100 text-green-800'
                          : 'bg-red-100 text-red-800'
                      }`}
                    >
                      {schedule.isAvailable ? 'Tersedia' : 'Tidak Tersedia'}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default DoctorScheduleList; 