{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\JOKI MAKALAH\\\\halobantu1\\\\halobantu\\\\src\\\\components\\\\common\\\\ProtectedRoute.js\";\nimport React from 'react';\nimport { Navigate } from 'react-router-dom';\nimport authService from '../../services/authService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProtectedRoute = ({\n  children,\n  requiredRole = null\n}) => {\n  const isAuthenticated = authService.isAuthenticated();\n  const currentUser = authService.getCurrentUser();\n\n  // Jika tidak login, redirect ke login\n  if (!isAuthenticated) {\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/login\",\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 12\n    }, this);\n  }\n\n  // Jika ada role requirement dan user tidak memiliki role yang sesuai\n  if (requiredRole && (currentUser === null || currentUser === void 0 ? void 0 : currentUser.role) !== requiredRole) {\n    // Redirect berdasarkan role user\n    if ((currentUser === null || currentUser === void 0 ? void 0 : currentUser.role) === 'admin') {\n      return /*#__PURE__*/_jsxDEV(Navigate, {\n        to: \"/admin\",\n        replace: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 14\n      }, this);\n    } else {\n      return /*#__PURE__*/_jsxDEV(Navigate, {\n        to: \"/dashboard\",\n        replace: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 14\n      }, this);\n    }\n  }\n  return children;\n};\n_c = ProtectedRoute;\nexport default ProtectedRoute;\nvar _c;\n$RefreshReg$(_c, \"ProtectedRoute\");", "map": {"version": 3, "names": ["React", "Navigate", "authService", "jsxDEV", "_jsxDEV", "ProtectedRoute", "children", "requiredRole", "isAuthenticated", "currentUser", "getCurrentUser", "to", "replace", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "role", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/JOKI MAKALAH/halobantu1/halobantu/src/components/common/ProtectedRoute.js"], "sourcesContent": ["import React from 'react';\nimport { Navigate } from 'react-router-dom';\nimport authService from '../../services/authService';\n\nconst ProtectedRoute = ({ children, requiredRole = null }) => {\n  const isAuthenticated = authService.isAuthenticated();\n  const currentUser = authService.getCurrentUser();\n\n  // Jika tidak login, redirect ke login\n  if (!isAuthenticated) {\n    return <Navigate to=\"/login\" replace />;\n  }\n\n  // Jika ada role requirement dan user tidak memiliki role yang sesuai\n  if (requiredRole && currentUser?.role !== requiredRole) {\n    // Redirect berdasarkan role user\n    if (currentUser?.role === 'admin') {\n      return <Navigate to=\"/admin\" replace />;\n    } else {\n      return <Navigate to=\"/dashboard\" replace />;\n    }\n  }\n\n  return children;\n};\n\nexport default ProtectedRoute;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,OAAOC,WAAW,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,MAAMC,cAAc,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,YAAY,GAAG;AAAK,CAAC,KAAK;EAC5D,MAAMC,eAAe,GAAGN,WAAW,CAACM,eAAe,CAAC,CAAC;EACrD,MAAMC,WAAW,GAAGP,WAAW,CAACQ,cAAc,CAAC,CAAC;;EAEhD;EACA,IAAI,CAACF,eAAe,EAAE;IACpB,oBAAOJ,OAAA,CAACH,QAAQ;MAACU,EAAE,EAAC,QAAQ;MAACC,OAAO;IAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACzC;;EAEA;EACA,IAAIT,YAAY,IAAI,CAAAE,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEQ,IAAI,MAAKV,YAAY,EAAE;IACtD;IACA,IAAI,CAAAE,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEQ,IAAI,MAAK,OAAO,EAAE;MACjC,oBAAOb,OAAA,CAACH,QAAQ;QAACU,EAAE,EAAC,QAAQ;QAACC,OAAO;MAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IACzC,CAAC,MAAM;MACL,oBAAOZ,OAAA,CAACH,QAAQ;QAACU,EAAE,EAAC,YAAY;QAACC,OAAO;MAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAC7C;EACF;EAEA,OAAOV,QAAQ;AACjB,CAAC;AAACY,EAAA,GApBIb,cAAc;AAsBpB,eAAeA,cAAc;AAAC,IAAAa,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}