const jwt = require('jsonwebtoken');
const User = require('../models/User');
const authConfig = require('../config/auth');

// Verify JWT token
const verifyToken = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader) {
      return res.status(401).json({
        error: 'Access Denied',
        message: 'No token provided'
      });
    }
    
    const token = authHeader.startsWith('Bearer ') 
      ? authHeader.slice(7) 
      : authHeader;
    
    if (!token) {
      return res.status(401).json({
        error: 'Access Denied',
        message: 'Invalid token format'
      });
    }
    
    const decoded = jwt.verify(token, authConfig.jwt.secret);
    
    // Get user from database
    const user = await User.findByPk(decoded.userId);
    
    if (!user) {
      return res.status(401).json({
        error: 'Access Denied',
        message: 'User not found'
      });
    }
    
    if (!user.isActive) {
      return res.status(401).json({
        error: 'Access Denied',
        message: 'Account is deactivated'
      });
    }
    
    if (user.isLocked()) {
      return res.status(401).json({
        error: 'Account Locked',
        message: 'Account is temporarily locked due to multiple failed login attempts'
      });
    }
    
    req.user = user;
    next();
    
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        error: 'Access Denied',
        message: 'Invalid token'
      });
    }
    
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        error: 'Access Denied',
        message: 'Token expired'
      });
    }
    
    console.error('Auth middleware error:', error);
    return res.status(500).json({
      error: 'Internal Server Error',
      message: 'Authentication failed'
    });
  }
};

// Check if user has required role
const requireRole = (roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        error: 'Access Denied',
        message: 'Authentication required'
      });
    }
    
    const userRoles = Array.isArray(roles) ? roles : [roles];
    
    if (!userRoles.includes(req.user.role)) {
      return res.status(403).json({
        error: 'Access Forbidden',
        message: 'Insufficient permissions'
      });
    }
    
    next();
  };
};

// Check if user is admin
const requireAdmin = requireRole(authConfig.roles.ADMIN);

// Check if user is psychologist
const requirePsychologist = requireRole([authConfig.roles.PSYCHOLOGIST, authConfig.roles.ADMIN]);

// Check if user owns the resource or is admin
const requireOwnershipOrAdmin = (getUserId) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        error: 'Access Denied',
        message: 'Authentication required'
      });
    }
    
    const resourceUserId = typeof getUserId === 'function' 
      ? getUserId(req) 
      : req.params.userId || req.params.id;
    
    if (req.user.role === authConfig.roles.ADMIN || 
        req.user.id.toString() === resourceUserId.toString()) {
      return next();
    }
    
    return res.status(403).json({
      error: 'Access Forbidden',
      message: 'You can only access your own resources'
    });
  };
};

// Optional authentication (doesn't fail if no token)
const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader) {
      return next();
    }
    
    const token = authHeader.startsWith('Bearer ') 
      ? authHeader.slice(7) 
      : authHeader;
    
    if (!token) {
      return next();
    }
    
    const decoded = jwt.verify(token, authConfig.jwt.secret);
    const user = await User.findByPk(decoded.userId);
    
    if (user && user.isActive && !user.isLocked()) {
      req.user = user;
    }
    
    next();
    
  } catch (error) {
    // Ignore token errors for optional auth
    next();
  }
};

module.exports = {
  verifyToken,
  requireRole,
  requireAdmin,
  requirePsychologist,
  requireOwnershipOrAdmin,
  optionalAuth
};
