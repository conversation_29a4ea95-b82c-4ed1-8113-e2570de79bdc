import React from 'react';
import { useNavigate } from 'react-router-dom';
import authService from '../services/authService';

const TestPage = () => {
  const navigate = useNavigate();
  const currentUser = authService.getCurrentUser();
  const isAuthenticated = authService.isAuthenticated();

  const handleLogout = async () => {
    await authService.logout();
    navigate('/login');
  };

  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-lg shadow p-6">
          <h1 className="text-2xl font-bold mb-4">🧪 Test Page - Debug Info</h1>
          
          <div className="space-y-4">
            <div className="p-4 bg-blue-50 rounded">
              <h3 className="font-semibold text-blue-800">Authentication Status:</h3>
              <p>Is Authenticated: <span className={isAuthenticated ? 'text-green-600' : 'text-red-600'}>{isAuthenticated ? 'Yes' : 'No'}</span></p>
            </div>

            <div className="p-4 bg-green-50 rounded">
              <h3 className="font-semibold text-green-800">Current User:</h3>
              <pre className="text-sm mt-2 bg-gray-100 p-2 rounded">
                {JSON.stringify(currentUser, null, 2)}
              </pre>
            </div>

            <div className="p-4 bg-yellow-50 rounded">
              <h3 className="font-semibold text-yellow-800">LocalStorage Data:</h3>
              <div className="text-sm mt-2 space-y-1">
                <p><strong>Token:</strong> {localStorage.getItem('token') ? 'Present' : 'Missing'}</p>
                <p><strong>User:</strong> {localStorage.getItem('user') ? 'Present' : 'Missing'}</p>
                <p><strong>RefreshToken:</strong> {localStorage.getItem('refreshToken') ? 'Present' : 'Missing'}</p>
              </div>
            </div>

            <div className="flex space-x-4">
              <button
                onClick={() => navigate('/login')}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                Go to Login
              </button>
              <button
                onClick={() => navigate('/dashboard')}
                className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
              >
                Go to Dashboard
              </button>
              <button
                onClick={() => navigate('/admin')}
                className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700"
              >
                Go to Admin
              </button>
              {isAuthenticated && (
                <button
                  onClick={handleLogout}
                  className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
                >
                  Logout
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestPage;
