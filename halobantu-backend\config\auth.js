require('dotenv').config();

const authConfig = {
  // JWT Configuration
  jwt: {
    secret: process.env.JWT_SECRET || 'fallback_secret_key',
    expiresIn: process.env.JWT_EXPIRE || '7d',
    refreshSecret: process.env.JWT_REFRESH_SECRET || 'fallback_refresh_secret',
    refreshExpiresIn: process.env.JWT_REFRESH_EXPIRE || '30d'
  },
  
  // Bcrypt Configuration
  bcrypt: {
    saltRounds: parseInt(process.env.BCRYPT_SALT_ROUNDS) || 12
  },
  
  // Password Requirements
  password: {
    minLength: 8,
    requireUppercase: true,
    requireLowercase: true,
    requireNumbers: true,
    requireSpecialChars: false
  },
  
  // Account Security
  security: {
    maxLoginAttempts: 5,
    lockoutDuration: 15 * 60 * 1000, // 15 minutes in milliseconds
    passwordResetExpiry: 60 * 60 * 1000, // 1 hour in milliseconds
    emailVerificationExpiry: 24 * 60 * 60 * 1000 // 24 hours in milliseconds
  },
  
  // User Roles
  roles: {
    USER: 'user',
    ADMIN: 'admin',
    PSYCHOLOGIST: 'psychologist'
  },
  
  // Default user settings
  defaults: {
    role: 'user',
    isActive: true,
    emailVerified: false
  }
};

module.exports = authConfig;
