{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\JOKI MAKALAH\\\\halobantu1\\\\halobantu\\\\src\\\\features\\\\dashboard\\\\components\\\\AboutSection.js\";\nimport React from 'react';\nimport { FaCheckCircle, FaUsers, FaAward, FaClock } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AboutSection = () => {\n  const stats = [{\n    icon: /*#__PURE__*/_jsxDEV(FaUsers, {\n      className: \"text-3xl text-blue-600\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 13\n    }, this),\n    number: \"10,000+\",\n    label: \"Pelanggan Puas\"\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(FaAward, {\n      className: \"text-3xl text-green-600\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 13\n    }, this),\n    number: \"5+\",\n    label: \"Tahun Pengalaman\"\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(FaCheckCircle, {\n      className: \"text-3xl text-purple-600\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 13\n    }, this),\n    number: \"50,000+\",\n    label: \"Layanan Selesai\"\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(FaClock, {\n      className: \"text-3xl text-orange-600\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 13\n    }, this),\n    number: \"24/7\",\n    label: \"Dukungan\"\n  }];\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    id: \"about\",\n    className: \"py-20 bg-white\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-4xl md:text-5xl font-bold text-gray-800 mb-6\",\n            children: \"Tentang Kami\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 34,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-lg text-gray-600 mb-6 leading-relaxed\",\n            children: \"Future X adalah perusahaan teknologi otomotif terdepan yang menghadirkan solusi inovatif untuk masa depan transportasi. Kami menggabungkan teknologi canggih dengan layanan berkualitas tinggi untuk memberikan pengalaman terbaik bagi pelanggan.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 37,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-lg text-gray-600 mb-8 leading-relaxed\",\n            children: \"Dengan tim ahli berpengalaman dan teknologi mutakhir, kami berkomitmen menghadirkan revolusi dalam industri otomotif. Visi kami adalah menciptakan ekosistem transportasi yang lebih cerdas, efisien, dan berkelanjutan.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4 mb-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-3\",\n              children: [/*#__PURE__*/_jsxDEV(FaCheckCircle, {\n                className: \"text-green-500 text-xl\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 51,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-gray-700\",\n                children: \"Teknologi otomotif terdepan\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 52,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 50,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-3\",\n              children: [/*#__PURE__*/_jsxDEV(FaCheckCircle, {\n                className: \"text-green-500 text-xl\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 55,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-gray-700\",\n                children: \"Solusi inovatif dan berkelanjutan\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 56,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 54,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-3\",\n              children: [/*#__PURE__*/_jsxDEV(FaCheckCircle, {\n                className: \"text-green-500 text-xl\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 59,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-gray-700\",\n                children: \"Tim ahli berpengalaman\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 60,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 58,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-3\",\n              children: [/*#__PURE__*/_jsxDEV(FaCheckCircle, {\n                className: \"text-green-500 text-xl\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 63,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-gray-700\",\n                children: \"Layanan premium berkualitas tinggi\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 64,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 62,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"px-8 py-4 bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-lg transition-all duration-300 transform hover:scale-105\",\n            children: \"Pelajari Lebih Lanjut\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative rounded-2xl overflow-hidden shadow-2xl\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: \"https://images.pexels.com/photos/3184465/pexels-photo-3184465.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop\",\n              alt: \"Tim HaloBantu\",\n              className: \"w-full h-96 object-cover\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 76,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-0 bg-gradient-to-t from-black/30 to-transparent\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 81,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute -bottom-8 -left-8 bg-white rounded-xl shadow-xl p-6 border\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-2 gap-4\",\n              children: stats.map((stat, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-center mb-2\",\n                  children: stat.icon\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 89,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-2xl font-bold text-gray-800\",\n                  children: stat.number\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 92,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-gray-600\",\n                  children: stat.label\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 93,\n                  columnNumber: 21\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 88,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 86,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 29,\n    columnNumber: 5\n  }, this);\n};\n_c = AboutSection;\nexport default AboutSection;\nvar _c;\n$RefreshReg$(_c, \"AboutSection\");", "map": {"version": 3, "names": ["React", "FaCheckCircle", "FaUsers", "FaAward", "FaClock", "jsxDEV", "_jsxDEV", "AboutSection", "stats", "icon", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "number", "label", "id", "children", "src", "alt", "map", "stat", "index", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/JOKI MAKALAH/halobantu1/halobantu/src/features/dashboard/components/AboutSection.js"], "sourcesContent": ["import React from 'react';\nimport { FaCheckCircle, FaUsers, FaAward, FaClock } from 'react-icons/fa';\n\nconst AboutSection = () => {\n  const stats = [\n    {\n      icon: <FaUsers className=\"text-3xl text-blue-600\" />,\n      number: \"10,000+\",\n      label: \"Pelanggan Puas\"\n    },\n    {\n      icon: <FaAward className=\"text-3xl text-green-600\" />,\n      number: \"5+\",\n      label: \"Tahun Pengalaman\"\n    },\n    {\n      icon: <FaCheckCircle className=\"text-3xl text-purple-600\" />,\n      number: \"50,000+\",\n      label: \"<PERSON>ana<PERSON>\"\n    },\n    {\n      icon: <FaClock className=\"text-3xl text-orange-600\" />,\n      number: \"24/7\",\n      label: \"Dukungan\"\n    }\n  ];\n\n  return (\n    <section id=\"about\" className=\"py-20 bg-white\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\">\n          {/* Left Content */}\n          <div>\n            <h2 className=\"text-4xl md:text-5xl font-bold text-gray-800 mb-6\">\n              Tentang Kami\n            </h2>\n            <p className=\"text-lg text-gray-600 mb-6 leading-relaxed\">\n              Future X adalah perusahaan teknologi otomotif terdepan yang menghadirkan solusi inovatif untuk\n              masa depan transportasi. Kami menggabungkan teknologi canggih dengan layanan berkualitas tinggi\n              untuk memberikan pengalaman terbaik bagi pelanggan.\n            </p>\n            <p className=\"text-lg text-gray-600 mb-8 leading-relaxed\">\n              Dengan tim ahli berpengalaman dan teknologi mutakhir, kami berkomitmen menghadirkan revolusi\n              dalam industri otomotif. Visi kami adalah menciptakan ekosistem transportasi yang lebih cerdas,\n              efisien, dan berkelanjutan.\n            </p>\n\n            {/* Features */}\n            <div className=\"space-y-4 mb-8\">\n              <div className=\"flex items-center space-x-3\">\n                <FaCheckCircle className=\"text-green-500 text-xl\" />\n                <span className=\"text-gray-700\">Teknologi otomotif terdepan</span>\n              </div>\n              <div className=\"flex items-center space-x-3\">\n                <FaCheckCircle className=\"text-green-500 text-xl\" />\n                <span className=\"text-gray-700\">Solusi inovatif dan berkelanjutan</span>\n              </div>\n              <div className=\"flex items-center space-x-3\">\n                <FaCheckCircle className=\"text-green-500 text-xl\" />\n                <span className=\"text-gray-700\">Tim ahli berpengalaman</span>\n              </div>\n              <div className=\"flex items-center space-x-3\">\n                <FaCheckCircle className=\"text-green-500 text-xl\" />\n                <span className=\"text-gray-700\">Layanan premium berkualitas tinggi</span>\n              </div>\n            </div>\n\n            <button className=\"px-8 py-4 bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-lg transition-all duration-300 transform hover:scale-105\">\n              Pelajari Lebih Lanjut\n            </button>\n          </div>\n\n          {/* Right Image */}\n          <div className=\"relative\">\n            <div className=\"relative rounded-2xl overflow-hidden shadow-2xl\">\n              <img\n                src=\"https://images.pexels.com/photos/3184465/pexels-photo-3184465.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop\"\n                alt=\"Tim HaloBantu\"\n                className=\"w-full h-96 object-cover\"\n              />\n              <div className=\"absolute inset-0 bg-gradient-to-t from-black/30 to-transparent\"></div>\n            </div>\n            \n            {/* Floating Stats Card */}\n            <div className=\"absolute -bottom-8 -left-8 bg-white rounded-xl shadow-xl p-6 border\">\n              <div className=\"grid grid-cols-2 gap-4\">\n                {stats.map((stat, index) => (\n                  <div key={index} className=\"text-center\">\n                    <div className=\"flex justify-center mb-2\">\n                      {stat.icon}\n                    </div>\n                    <div className=\"text-2xl font-bold text-gray-800\">{stat.number}</div>\n                    <div className=\"text-sm text-gray-600\">{stat.label}</div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default AboutSection;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,EAAEC,OAAO,EAAEC,OAAO,EAAEC,OAAO,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1E,MAAMC,YAAY,GAAGA,CAAA,KAAM;EACzB,MAAMC,KAAK,GAAG,CACZ;IACEC,IAAI,eAAEH,OAAA,CAACJ,OAAO;MAACQ,SAAS,EAAC;IAAwB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACpDC,MAAM,EAAE,SAAS;IACjBC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,IAAI,eAAEH,OAAA,CAACH,OAAO;MAACO,SAAS,EAAC;IAAyB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACrDC,MAAM,EAAE,IAAI;IACZC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,IAAI,eAAEH,OAAA,CAACL,aAAa;MAACS,SAAS,EAAC;IAA0B;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC5DC,MAAM,EAAE,SAAS;IACjBC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,IAAI,eAAEH,OAAA,CAACF,OAAO;MAACM,SAAS,EAAC;IAA0B;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtDC,MAAM,EAAE,MAAM;IACdC,KAAK,EAAE;EACT,CAAC,CACF;EAED,oBACEV,OAAA;IAASW,EAAE,EAAC,OAAO;IAACP,SAAS,EAAC,gBAAgB;IAAAQ,QAAA,eAC5CZ,OAAA;MAAKI,SAAS,EAAC,wBAAwB;MAAAQ,QAAA,eACrCZ,OAAA;QAAKI,SAAS,EAAC,qDAAqD;QAAAQ,QAAA,gBAElEZ,OAAA;UAAAY,QAAA,gBACEZ,OAAA;YAAII,SAAS,EAAC,mDAAmD;YAAAQ,QAAA,EAAC;UAElE;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLR,OAAA;YAAGI,SAAS,EAAC,4CAA4C;YAAAQ,QAAA,EAAC;UAI1D;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJR,OAAA;YAAGI,SAAS,EAAC,4CAA4C;YAAAQ,QAAA,EAAC;UAI1D;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAGJR,OAAA;YAAKI,SAAS,EAAC,gBAAgB;YAAAQ,QAAA,gBAC7BZ,OAAA;cAAKI,SAAS,EAAC,6BAA6B;cAAAQ,QAAA,gBAC1CZ,OAAA,CAACL,aAAa;gBAACS,SAAS,EAAC;cAAwB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACpDR,OAAA;gBAAMI,SAAS,EAAC,eAAe;gBAAAQ,QAAA,EAAC;cAA2B;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/D,CAAC,eACNR,OAAA;cAAKI,SAAS,EAAC,6BAA6B;cAAAQ,QAAA,gBAC1CZ,OAAA,CAACL,aAAa;gBAACS,SAAS,EAAC;cAAwB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACpDR,OAAA;gBAAMI,SAAS,EAAC,eAAe;gBAAAQ,QAAA,EAAC;cAAiC;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrE,CAAC,eACNR,OAAA;cAAKI,SAAS,EAAC,6BAA6B;cAAAQ,QAAA,gBAC1CZ,OAAA,CAACL,aAAa;gBAACS,SAAS,EAAC;cAAwB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACpDR,OAAA;gBAAMI,SAAS,EAAC,eAAe;gBAAAQ,QAAA,EAAC;cAAsB;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CAAC,eACNR,OAAA;cAAKI,SAAS,EAAC,6BAA6B;cAAAQ,QAAA,gBAC1CZ,OAAA,CAACL,aAAa;gBAACS,SAAS,EAAC;cAAwB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACpDR,OAAA;gBAAMI,SAAS,EAAC,eAAe;gBAAAQ,QAAA,EAAC;cAAkC;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENR,OAAA;YAAQI,SAAS,EAAC,mIAAmI;YAAAQ,QAAA,EAAC;UAEtJ;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGNR,OAAA;UAAKI,SAAS,EAAC,UAAU;UAAAQ,QAAA,gBACvBZ,OAAA;YAAKI,SAAS,EAAC,iDAAiD;YAAAQ,QAAA,gBAC9DZ,OAAA;cACEa,GAAG,EAAC,mHAAmH;cACvHC,GAAG,EAAC,eAAe;cACnBV,SAAS,EAAC;YAA0B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CAAC,eACFR,OAAA;cAAKI,SAAS,EAAC;YAAgE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnF,CAAC,eAGNR,OAAA;YAAKI,SAAS,EAAC,qEAAqE;YAAAQ,QAAA,eAClFZ,OAAA;cAAKI,SAAS,EAAC,wBAAwB;cAAAQ,QAAA,EACpCV,KAAK,CAACa,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACrBjB,OAAA;gBAAiBI,SAAS,EAAC,aAAa;gBAAAQ,QAAA,gBACtCZ,OAAA;kBAAKI,SAAS,EAAC,0BAA0B;kBAAAQ,QAAA,EACtCI,IAAI,CAACb;gBAAI;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC,eACNR,OAAA;kBAAKI,SAAS,EAAC,kCAAkC;kBAAAQ,QAAA,EAAEI,IAAI,CAACP;gBAAM;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACrER,OAAA;kBAAKI,SAAS,EAAC,uBAAuB;kBAAAQ,QAAA,EAAEI,IAAI,CAACN;gBAAK;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA,GALjDS,KAAK;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAMV,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAACU,EAAA,GAnGIjB,YAAY;AAqGlB,eAAeA,YAAY;AAAC,IAAAiB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}