import React from 'react';
import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom';
import Layout from './components/layout/Layout';
import ProtectedRoute from './components/common/ProtectedRoute';
import Login from './features/auth/Login';
import Register from './features/auth/Register';
import Dashboard from './features/dashboard/Dashboard';
import PsychologyPage from './features/psychology/pages/PsychologyPage';
import ConsultationDetail from './features/psychology/pages/ConsultationDetail';
import DoctorBiography from './features/psychology/pages/DoctorBiography';
import WorkshopPage from './features/workshop/WorkshopPage';
import DailyServicesPage from './features/daily-services/DailyServicesPage';
import AdminDashboard from './features/admin/AdminDashboard';
import './styles/App.css';

function App() {
  return (
    <BrowserRouter>
      <Routes>
        {/* Redirect root to login */}
        <Route path="/" element={<Navigate to="/login" replace />} />

        {/* Auth Routes - No Layout */}
        <Route path="/login" element={<Login />} />
        <Route path="/register" element={<Register />} />

        {/* Protected Routes - User Dashboard */}
        <Route
          path="/dashboard"
          element={
            <ProtectedRoute requiredRole="user">
              <Layout><Dashboard /></Layout>
            </ProtectedRoute>
          }
        />

        {/* Protected Routes - Admin Dashboard */}
        <Route
          path="/admin"
          element={
            <ProtectedRoute requiredRole="admin">
              <AdminDashboard />
            </ProtectedRoute>
          }
        />

        {/* Other Protected Routes - With Layout (for future features) */}
        <Route
          path="/workshop"
          element={
            <ProtectedRoute>
              <Layout><WorkshopPage /></Layout>
            </ProtectedRoute>
          }
        />
        <Route
          path="/psychology"
          element={
            <ProtectedRoute>
              <Layout><PsychologyPage /></Layout>
            </ProtectedRoute>
          }
        />
        <Route
          path="/psychology/consultation"
          element={
            <ProtectedRoute>
              <Layout><ConsultationDetail /></Layout>
            </ProtectedRoute>
          }
        />
        <Route
          path="/psychology/doctor/:id"
          element={
            <ProtectedRoute>
              <DoctorBiography />
            </ProtectedRoute>
          }
        />
        <Route
          path="/services"
          element={
            <ProtectedRoute>
              <Layout><Dashboard /></Layout>
            </ProtectedRoute>
          }
        />
        <Route
          path="/daily-services"
          element={
            <ProtectedRoute>
              <Layout><DailyServicesPage /></Layout>
            </ProtectedRoute>
          }
        />
      </Routes>
    </BrowserRouter>
  );
}

export default App;
