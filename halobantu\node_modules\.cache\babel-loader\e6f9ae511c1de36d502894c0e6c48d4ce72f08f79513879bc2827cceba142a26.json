{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\JOKI MAKALAH\\\\halobantu1\\\\halobantu\\\\src\\\\components\\\\TestPage.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport authService from '../services/authService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TestPage = () => {\n  _s();\n  const navigate = useNavigate();\n  const currentUser = authService.getCurrentUser();\n  const isAuthenticated = authService.isAuthenticated();\n  const handleLogout = async () => {\n    await authService.logout();\n    navigate('/login');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-100 p-8\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-4xl mx-auto\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-bold mb-4\",\n          children: \"\\uD83E\\uDDEA Test Page - Debug Info\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-4 bg-blue-50 rounded\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"font-semibold text-blue-800\",\n              children: \"Authentication Status:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 23,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [\"Is Authenticated: \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: isAuthenticated ? 'text-green-600' : 'text-red-600',\n                children: isAuthenticated ? 'Yes' : 'No'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 24,\n                columnNumber: 36\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 24,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 22,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-4 bg-green-50 rounded\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"font-semibold text-green-800\",\n              children: \"Current User:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 28,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"pre\", {\n              className: \"text-sm mt-2 bg-gray-100 p-2 rounded\",\n              children: JSON.stringify(currentUser, null, 2)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 29,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 27,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-4 bg-yellow-50 rounded\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"font-semibold text-yellow-800\",\n              children: \"LocalStorage Data:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 35,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm mt-2 space-y-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Token:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 37,\n                  columnNumber: 20\n                }, this), \" \", localStorage.getItem('token') ? 'Present' : 'Missing']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 37,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"User:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 38,\n                  columnNumber: 20\n                }, this), \" \", localStorage.getItem('user') ? 'Present' : 'Missing']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 38,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"RefreshToken:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 39,\n                  columnNumber: 20\n                }, this), \" \", localStorage.getItem('refreshToken') ? 'Present' : 'Missing']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 39,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 36,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 34,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => navigate('/login'),\n              className: \"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700\",\n              children: \"Go to Login\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 44,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => navigate('/dashboard'),\n              className: \"px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700\",\n              children: \"Go to Dashboard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 50,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => navigate('/admin'),\n              className: \"px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700\",\n              children: \"Go to Admin\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 56,\n              columnNumber: 15\n            }, this), isAuthenticated && /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleLogout,\n              className: \"px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700\",\n              children: \"Logout\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 63,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 16,\n    columnNumber: 5\n  }, this);\n};\n_s(TestPage, \"CzcTeTziyjMsSrAVmHuCCb6+Bfg=\", false, function () {\n  return [useNavigate];\n});\n_c = TestPage;\nexport default TestPage;\nvar _c;\n$RefreshReg$(_c, \"TestPage\");", "map": {"version": 3, "names": ["React", "useNavigate", "authService", "jsxDEV", "_jsxDEV", "TestPage", "_s", "navigate", "currentUser", "getCurrentUser", "isAuthenticated", "handleLogout", "logout", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "JSON", "stringify", "localStorage", "getItem", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/JOKI MAKALAH/halobantu1/halobantu/src/components/TestPage.js"], "sourcesContent": ["import React from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport authService from '../services/authService';\n\nconst TestPage = () => {\n  const navigate = useNavigate();\n  const currentUser = authService.getCurrentUser();\n  const isAuthenticated = authService.isAuthenticated();\n\n  const handleLogout = async () => {\n    await authService.logout();\n    navigate('/login');\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-100 p-8\">\n      <div className=\"max-w-4xl mx-auto\">\n        <div className=\"bg-white rounded-lg shadow p-6\">\n          <h1 className=\"text-2xl font-bold mb-4\">🧪 Test Page - Debug Info</h1>\n          \n          <div className=\"space-y-4\">\n            <div className=\"p-4 bg-blue-50 rounded\">\n              <h3 className=\"font-semibold text-blue-800\">Authentication Status:</h3>\n              <p>Is Authenticated: <span className={isAuthenticated ? 'text-green-600' : 'text-red-600'}>{isAuthenticated ? 'Yes' : 'No'}</span></p>\n            </div>\n\n            <div className=\"p-4 bg-green-50 rounded\">\n              <h3 className=\"font-semibold text-green-800\">Current User:</h3>\n              <pre className=\"text-sm mt-2 bg-gray-100 p-2 rounded\">\n                {JSON.stringify(currentUser, null, 2)}\n              </pre>\n            </div>\n\n            <div className=\"p-4 bg-yellow-50 rounded\">\n              <h3 className=\"font-semibold text-yellow-800\">LocalStorage Data:</h3>\n              <div className=\"text-sm mt-2 space-y-1\">\n                <p><strong>Token:</strong> {localStorage.getItem('token') ? 'Present' : 'Missing'}</p>\n                <p><strong>User:</strong> {localStorage.getItem('user') ? 'Present' : 'Missing'}</p>\n                <p><strong>RefreshToken:</strong> {localStorage.getItem('refreshToken') ? 'Present' : 'Missing'}</p>\n              </div>\n            </div>\n\n            <div className=\"flex space-x-4\">\n              <button\n                onClick={() => navigate('/login')}\n                className=\"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700\"\n              >\n                Go to Login\n              </button>\n              <button\n                onClick={() => navigate('/dashboard')}\n                className=\"px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700\"\n              >\n                Go to Dashboard\n              </button>\n              <button\n                onClick={() => navigate('/admin')}\n                className=\"px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700\"\n              >\n                Go to Admin\n              </button>\n              {isAuthenticated && (\n                <button\n                  onClick={handleLogout}\n                  className=\"px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700\"\n                >\n                  Logout\n                </button>\n              )}\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default TestPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,WAAW,MAAM,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAMC,QAAQ,GAAGN,WAAW,CAAC,CAAC;EAC9B,MAAMO,WAAW,GAAGN,WAAW,CAACO,cAAc,CAAC,CAAC;EAChD,MAAMC,eAAe,GAAGR,WAAW,CAACQ,eAAe,CAAC,CAAC;EAErD,MAAMC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,MAAMT,WAAW,CAACU,MAAM,CAAC,CAAC;IAC1BL,QAAQ,CAAC,QAAQ,CAAC;EACpB,CAAC;EAED,oBACEH,OAAA;IAAKS,SAAS,EAAC,8BAA8B;IAAAC,QAAA,eAC3CV,OAAA;MAAKS,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eAChCV,OAAA;QAAKS,SAAS,EAAC,gCAAgC;QAAAC,QAAA,gBAC7CV,OAAA;UAAIS,SAAS,EAAC,yBAAyB;UAAAC,QAAA,EAAC;QAAyB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAEtEd,OAAA;UAAKS,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBV,OAAA;YAAKS,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACrCV,OAAA;cAAIS,SAAS,EAAC,6BAA6B;cAAAC,QAAA,EAAC;YAAsB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvEd,OAAA;cAAAU,QAAA,GAAG,oBAAkB,eAAAV,OAAA;gBAAMS,SAAS,EAAEH,eAAe,GAAG,gBAAgB,GAAG,cAAe;gBAAAI,QAAA,EAAEJ,eAAe,GAAG,KAAK,GAAG;cAAI;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnI,CAAC,eAENd,OAAA;YAAKS,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtCV,OAAA;cAAIS,SAAS,EAAC,8BAA8B;cAAAC,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/Dd,OAAA;cAAKS,SAAS,EAAC,sCAAsC;cAAAC,QAAA,EAClDK,IAAI,CAACC,SAAS,CAACZ,WAAW,EAAE,IAAI,EAAE,CAAC;YAAC;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENd,OAAA;YAAKS,SAAS,EAAC,0BAA0B;YAAAC,QAAA,gBACvCV,OAAA;cAAIS,SAAS,EAAC,+BAA+B;cAAAC,QAAA,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrEd,OAAA;cAAKS,SAAS,EAAC,wBAAwB;cAAAC,QAAA,gBACrCV,OAAA;gBAAAU,QAAA,gBAAGV,OAAA;kBAAAU,QAAA,EAAQ;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACG,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,GAAG,SAAS,GAAG,SAAS;cAAA;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACtFd,OAAA;gBAAAU,QAAA,gBAAGV,OAAA;kBAAAU,QAAA,EAAQ;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACG,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,GAAG,SAAS,GAAG,SAAS;cAAA;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpFd,OAAA;gBAAAU,QAAA,gBAAGV,OAAA;kBAAAU,QAAA,EAAQ;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACG,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC,GAAG,SAAS,GAAG,SAAS;cAAA;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENd,OAAA;YAAKS,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BV,OAAA;cACEmB,OAAO,EAAEA,CAAA,KAAMhB,QAAQ,CAAC,QAAQ,CAAE;cAClCM,SAAS,EAAC,4DAA4D;cAAAC,QAAA,EACvE;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTd,OAAA;cACEmB,OAAO,EAAEA,CAAA,KAAMhB,QAAQ,CAAC,YAAY,CAAE;cACtCM,SAAS,EAAC,8DAA8D;cAAAC,QAAA,EACzE;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTd,OAAA;cACEmB,OAAO,EAAEA,CAAA,KAAMhB,QAAQ,CAAC,QAAQ,CAAE;cAClCM,SAAS,EAAC,gEAAgE;cAAAC,QAAA,EAC3E;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EACRR,eAAe,iBACdN,OAAA;cACEmB,OAAO,EAAEZ,YAAa;cACtBE,SAAS,EAAC,0DAA0D;cAAAC,QAAA,EACrE;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACZ,EAAA,CAvEID,QAAQ;EAAA,QACKJ,WAAW;AAAA;AAAAuB,EAAA,GADxBnB,QAAQ;AAyEd,eAAeA,QAAQ;AAAC,IAAAmB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}